# Findy.AI - Full Stack Application with AWS CDK

A complete full-stack application with React frontend and AWS backend infrastructure including DynamoDB, ECS, Cognito authentication, and API Gateway.

## Architecture Overview

### Backend Infrastructure (AWS CDK)
- **AWS Cognito**: User authentication and authorization
- **DynamoDB**: NoSQL database for tasks, users, and sessions
- **ECS Fargate**: Containerized API service
- **API Gateway**: REST API with Cognito authorization
- **Application Load Balancer**: Load balancing for ECS service
- **CloudWatch**: Logging and monitoring

### Frontend (React)
- **React 18**: Modern React with hooks
- **TypeScript**: Type-safe development
- **Styled Components**: CSS-in-JS styling
- **Authentication Context**: Centralized auth state management
- **API Service**: Backend communication layer

## Project Structure

```
Workplace/
├── findy-agent/                 # React frontend application
│   ├── src/
│   │   ├── components/
│   │   │   ├── auth/           # Authentication components
│   │   │   ├── WelcomeScreen.tsx
│   │   │   ├── TaskBar.tsx
│   │   │   └── UserProfile.tsx
│   │   ├── contexts/
│   │   │   └── AuthContext.tsx # Authentication context
│   │   ├── services/
│   │   │   └── api.ts          # API service layer
│   │   └── config/
│   │       └── aws-config.ts   # AWS configuration
│   └── package.json
├── findy-backend/              # AWS CDK infrastructure
│   ├── lib/
│   │   ├── findy-core-stack.ts    # Core infrastructure (DynamoDB, Cognito, VPC)
│   │   ├── findy-ecs-stack.ts     # ECS infrastructure
│   │   └── findy-api-stack.ts     # API Gateway
│   ├── api-service/            # Express.js API service
│   │   ├── src/
│   │   │   ├── routes/         # API routes
│   │   │   ├── services/       # Business logic
│   │   │   ├── middleware/     # Auth middleware
│   │   │   └── types/          # TypeScript types
│   │   ├── Dockerfile          # Container configuration
│   │   └── package.json
│   ├── deploy*.sh              # Deployment scripts
│   └── package.json
└── README.md
```

## Prerequisites

1. **Node.js** (v18 or later)
2. **AWS CLI** configured with appropriate permissions
3. **AWS CDK** installed globally: `npm install -g aws-cdk`
4. **Docker** (for building API service image)
5. **jq** (for parsing JSON in deployment script)

## Quick Start

### 1. Deploy Backend Infrastructure

The infrastructure is split into separate stacks for better reliability and modularity:

#### Option A: Deploy All at Once
```bash
cd findy-backend
./deploy.sh
```

#### Option B: Deploy Step by Step (Recommended)
```bash
cd findy-backend

# Step 1: Deploy core infrastructure (DynamoDB, Cognito, VPC)
./deploy-core.sh

# Step 2: Deploy ECS infrastructure
./deploy-ecs.sh

# Step 3: Deploy API Gateway
./deploy-api.sh
```

#### Option C: Deploy Core First, Then Remaining
```bash
cd findy-backend

# Deploy core infrastructure first
./deploy-core.sh

# Deploy ECS and API Gateway together
./deploy-remaining.sh
```

Each script will:
- Install CDK dependencies
- Build the CDK project
- Bootstrap CDK (if needed)
- Deploy the specific AWS infrastructure
- Create environment configuration for frontend (final step only)

### 2. Build and Deploy API Service

```bash
cd findy-backend/api-service

# Install dependencies
npm install

# Build the application
npm run build

# Build Docker image
docker build -t findy-api-service .

# Tag and push to ECR (replace with your ECR URI)
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <your-ecr-uri>
docker tag findy-api-service:latest <your-ecr-uri>/findy-api-service:latest
docker push <your-ecr-uri>/findy-api-service:latest
```

### 3. Update ECS Service

Update the ECS service to use your API image instead of the placeholder nginx image.

### 4. Start Frontend Development

```bash
cd findy-agent

# Install dependencies (if not already done)
npm install

# Start development server
npm start
```

## Features

### Authentication Flow
1. **Unauthenticated State**: Users see "Sign In to Start" button
2. **Login/Signup**: Modal with form validation
3. **Authenticated State**: Users can create and manage tasks
4. **Session Management**: Persistent login with JWT tokens

### Task Management
- Create new tasks with title and description
- View all user tasks
- Update task status and progress
- Delete tasks
- Real-time task synchronization

### Security
- JWT-based authentication with AWS Cognito
- API Gateway with Cognito authorizer
- CORS configuration for cross-origin requests
- Secure token storage and management

## Deployment Strategies

### Modular Deployment Benefits
- **Reliability**: If ECS deployment fails, core infrastructure remains intact
- **Faster Iterations**: Deploy only what changed
- **Easier Debugging**: Isolate issues to specific components
- **Cost Optimization**: Avoid redeploying expensive resources

### Stack Dependencies
1. **Core Stack**: Independent (DynamoDB, Cognito, VPC)
2. **ECS Stack**: Depends on Core Stack
3. **API Stack**: Depends on both Core and ECS Stacks

## API Endpoints

### Authentication
- Handled by AWS Cognito User Pool

### Tasks API
- `GET /api/tasks` - Get all user tasks
- `POST /api/tasks` - Create new task
- `GET /api/tasks/:taskId` - Get specific task
- `PUT /api/tasks/:taskId` - Update task
- `DELETE /api/tasks/:taskId` - Delete task

### Health Check
- `GET /health` - Service health check

## Environment Variables

### Frontend (.env.local)
```
REACT_APP_AWS_REGION=us-east-1
REACT_APP_USER_POOL_ID=us-east-1_xxxxxxxxx
REACT_APP_USER_POOL_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx
REACT_APP_IDENTITY_POOL_ID=us-east-1:xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
REACT_APP_API_GATEWAY_URL=https://xxxxxxxxxx.execute-api.us-east-1.amazonaws.com/prod
```

### Backend (ECS Environment)
```
NODE_ENV=production
TASKS_TABLE_NAME=findy-tasks
USERS_TABLE_NAME=findy-users
SESSIONS_TABLE_NAME=findy-sessions
USER_POOL_ID=us-east-1_xxxxxxxxx
USER_POOL_CLIENT_ID=xxxxxxxxxxxxxxxxxxxxxxxxxx
AWS_REGION=us-east-1
```

## Development

### Frontend Development
```bash
cd findy-agent
npm start
```

### Backend API Development
```bash
cd findy-backend/api-service
npm run dev
```

### CDK Development
```bash
cd findy-backend
npm run watch  # Watch for changes
npx cdk diff   # See changes
npx cdk deploy FindyCoreStack    # Deploy specific stack
npx cdk deploy FindyEcsStack     # Deploy ECS stack
npx cdk deploy FindyApiStack     # Deploy API stack
```

## Testing

### Frontend Tests
```bash
cd findy-agent
npm test
```

### Backend Tests
```bash
cd findy-backend/api-service
npm test
```

## Deployment

### Production Deployment
1. Deploy infrastructure: Choose your deployment strategy above
2. Build and push API image to ECR
3. Update ECS service with new image
4. Build and deploy frontend to S3/CloudFront

### Cleanup
```bash
cd findy-backend
npx cdk destroy FindyApiStack
npx cdk destroy FindyEcsStack
npx cdk destroy FindyCoreStack
```

## Troubleshooting

### Common Issues

1. **CDK Bootstrap Error**: Run `npx cdk bootstrap` in your AWS account
2. **Permission Errors**: Ensure AWS CLI has sufficient permissions
3. **ECS Deployment Stuck**: Use modular deployment to isolate ECS issues
4. **CORS Issues**: Check API Gateway CORS configuration
5. **Authentication Errors**: Verify Cognito configuration

### Deployment Scripts
- `deploy-core.sh`: Deploy only core infrastructure
- `deploy-ecs.sh`: Deploy only ECS (requires core)
- `deploy-api.sh`: Deploy only API Gateway (requires core + ECS)
- `deploy-remaining.sh`: Deploy ECS + API (requires core)
- `deploy.sh`: Deploy everything at once

### Logs
- **ECS Logs**: Check CloudWatch logs for API service
- **API Gateway Logs**: Enable logging in API Gateway
- **Frontend Logs**: Check browser console

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions, please create an issue in the repository.

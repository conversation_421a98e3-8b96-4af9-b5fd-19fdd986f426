// API service for communicating with the backend
export interface Task {
  userId: string;
  taskId: string;
  title: string;
  description: string;
  status: 'active' | 'completed' | 'paused';
  createdAt: string;
  updatedAt: string;
  progress?: number;
}

export interface CreateTaskRequest {
  title: string;
  description: string;
}

export interface UpdateTaskRequest {
  title?: string;
  description?: string;
  status?: 'active' | 'completed' | 'paused';
  progress?: number;
}

class ApiService {
  private baseUrl: string;

  constructor() {
    // This will be replaced with the actual API Gateway URL from CDK deployment
    this.baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001';
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = localStorage.getItem('authToken');
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(`${this.baseUrl}${endpoint}`, config);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // Task operations
  async getTasks(): Promise<{ tasks: Task[] }> {
    return this.request<{ tasks: Task[] }>('/api/tasks');
  }

  async getTask(taskId: string): Promise<{ task: Task }> {
    return this.request<{ task: Task }>(`/api/tasks/${taskId}`);
  }

  async createTask(data: CreateTaskRequest): Promise<{ task: Task }> {
    return this.request<{ task: Task }>('/api/tasks', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateTask(taskId: string, data: UpdateTaskRequest): Promise<{ task: Task }> {
    return this.request<{ task: Task }>(`/api/tasks/${taskId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteTask(taskId: string): Promise<{ message: string }> {
    return this.request<{ message: string }>(`/api/tasks/${taskId}`, {
      method: 'DELETE',
    });
  }

  // Health check
  async healthCheck(): Promise<{ status: string }> {
    return this.request<{ status: string }>('/health');
  }
}

export const apiService = new ApiService();

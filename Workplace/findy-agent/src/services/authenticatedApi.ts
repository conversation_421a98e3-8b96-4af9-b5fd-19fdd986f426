import { AuthContextType } from '../contexts/AuthContext';

export interface Task {
  userId: string;
  taskId: string;
  title: string;
  description: string;
  status: 'active' | 'completed' | 'paused';
  createdAt: string;
  updatedAt: string;
  progress?: number;
}

export class AuthenticatedApiService {
  private baseUrl: string;
  private authContext: AuthContextType;

  constructor(authContext: AuthContextType) {
    this.authContext = authContext;
    // This will be replaced with the actual API Gateway URL from CDK deployment
    this.baseUrl = process.env.REACT_APP_API_URL || "http://localhost:3001";
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    // Get token from auth context
    const token = await this.authContext.getAuthToken();
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(`${this.baseUrl}${endpoint}`, config);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      
      // Handle authentication errors specifically
      if (response.status === 401) {
        throw new Error('Authentication required. Please sign in.');
      }
      
      throw new Error(
        errorData.error || `HTTP error! status: ${response.status}`
      );
    }

    return response.json();
  }

  // Task operations
  async getTasks(): Promise<{ tasks: Task[] }> {
    if (!this.authContext.isAuthenticated) {
      throw new Error('User must be authenticated to access tasks');
    }
    return this.request<{ tasks: Task[] }>('/api/tasks');
  }

  async createTask(title: string, description: string): Promise<{ task: Task }> {
    if (!this.authContext.isAuthenticated) {
      throw new Error('User must be authenticated to create tasks');
    }
    return this.request<{ task: Task }>('/api/tasks', {
      method: 'POST',
      body: JSON.stringify({ title, description }),
    });
  }

  async getTask(taskId: string): Promise<{ task: Task }> {
    if (!this.authContext.isAuthenticated) {
      throw new Error('User must be authenticated to access tasks');
    }
    return this.request<{ task: Task }>(`/api/tasks/${taskId}`);
  }

  async updateTask(
    taskId: string,
    updates: Partial<Pick<Task, 'title' | 'description' | 'status' | 'progress'>>
  ): Promise<{ task: Task }> {
    if (!this.authContext.isAuthenticated) {
      throw new Error('User must be authenticated to update tasks');
    }
    return this.request<{ task: Task }>(`/api/tasks/${taskId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async deleteTask(taskId: string): Promise<{ message: string }> {
    if (!this.authContext.isAuthenticated) {
      throw new Error('User must be authenticated to delete tasks');
    }
    return this.request<{ message: string }>(`/api/tasks/${taskId}`, {
      method: 'DELETE',
    });
  }
}

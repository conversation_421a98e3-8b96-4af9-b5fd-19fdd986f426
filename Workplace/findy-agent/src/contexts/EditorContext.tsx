import React, {
	createContext,
	useContext,
	useState,
	useCallback,
	ReactNode,
} from "react";
import { useFileSystem } from "./FileSystemContext";

export interface EditorTab {
	id: string;
	filePath: string;
	fileName: string;
	content: string;
	isDirty: boolean;
	language?: string;
	fileHandle?: FileSystemFileHandle;
}

interface EditorContextType {
	tabs: EditorTab[];
	activeTabId: string | null;
	openFile: (
		filePath: string,
		content: string,
		fileName: string,
		fileHandle?: FileSystemFileHandle
	) => void;
	closeTab: (tabId: string) => void;
	setActiveTab: (tabId: string) => void;
	updateTabContent: (tabId: string, content: string) => void;
	saveTab: (tabId: string) => Promise<void>;
	saveAllTabs: () => Promise<void>;
	getActiveTab: () => EditorTab | null;
}

const EditorContext = createContext<EditorContextType | undefined>(undefined);

export const useEditor = () => {
	const context = useContext(EditorContext);
	if (context === undefined) {
		throw new Error("useEditor must be used within an EditorProvider");
	}
	return context;
};

interface EditorProviderProps {
	children: ReactNode;
}

export const EditorProvider: React.FC<EditorProviderProps> = ({ children }) => {
	const [tabs, setTabs] = useState<EditorTab[]>([]);
	const [activeTabId, setActiveTabId] = useState<string | null>(null);
	const { writeFile: writeFileToSystem } = useFileSystem();

	const generateTabId = useCallback(() => {
		return `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
	}, []);

	const getLanguageFromFileName = useCallback((fileName: string): string => {
		const extension = fileName.split(".").pop()?.toLowerCase();
		switch (extension) {
			case "js":
			case "jsx":
				return "javascript";
			case "ts":
			case "tsx":
				return "typescript";
			case "html":
			case "htm":
				return "html";
			case "css":
				return "css";
			case "scss":
			case "sass":
				return "scss";
			case "json":
				return "json";
			case "md":
				return "markdown";
			case "py":
				return "python";
			case "java":
				return "java";
			case "cpp":
			case "cc":
			case "cxx":
				return "cpp";
			case "c":
				return "c";
			case "cs":
				return "csharp";
			case "php":
				return "php";
			case "rb":
				return "ruby";
			case "go":
				return "go";
			case "rs":
				return "rust";
			case "xml":
				return "xml";
			case "yaml":
			case "yml":
				return "yaml";
			default:
				return "plaintext";
		}
	}, []);

	const openFile = useCallback(
		(
			filePath: string,
			content: string,
			fileName: string,
			fileHandle?: FileSystemFileHandle
		) => {
			// Check if file is already open
			const existingTab = tabs.find((tab) => tab.filePath === filePath);
			if (existingTab) {
				setActiveTabId(existingTab.id);
				return;
			}

			const newTab: EditorTab = {
				id: generateTabId(),
				filePath,
				fileName,
				content,
				isDirty: false,
				language: getLanguageFromFileName(fileName),
				fileHandle,
			};

			setTabs((prev) => [...prev, newTab]);
			setActiveTabId(newTab.id);
		},
		[tabs, generateTabId, getLanguageFromFileName]
	);

	const closeTab = useCallback(
		(tabId: string) => {
			setTabs((prev) => {
				const newTabs = prev.filter((tab) => tab.id !== tabId);

				// If closing the active tab, set a new active tab
				if (activeTabId === tabId) {
					const tabIndex = prev.findIndex((tab) => tab.id === tabId);
					if (newTabs.length > 0) {
						// Try to activate the tab to the right, or the last tab if closing the rightmost
						const newActiveIndex = Math.min(tabIndex, newTabs.length - 1);
						setActiveTabId(newTabs[newActiveIndex].id);
					} else {
						setActiveTabId(null);
					}
				}

				return newTabs;
			});
		},
		[activeTabId]
	);

	const setActiveTab = useCallback((tabId: string) => {
		setActiveTabId(tabId);
	}, []);

	const updateTabContent = useCallback((tabId: string, content: string) => {
		setTabs((prev) =>
			prev.map((tab) => {
				if (tab.id === tabId) {
					return {
						...tab,
						content,
						isDirty: true,
					};
				}
				return tab;
			})
		);
	}, []);

	const saveTab = useCallback(
		async (tabId: string) => {
			const tab = tabs.find((t) => t.id === tabId);
			if (!tab) return;

			if (tab.fileHandle) {
				try {
					await writeFileToSystem(tab.fileHandle, tab.content);
					console.log("File saved successfully:", tab.fileName);

					setTabs((prev) =>
						prev.map((t) => {
							if (t.id === tabId) {
								return { ...t, isDirty: false };
							}
							return t;
						})
					);
				} catch (error) {
					console.error("Error saving file:", error);
					alert("Failed to save file: " + (error as Error).message);
				}
			} else {
				// Handle save as for new files
				console.log("File has no handle, would need Save As dialog");
			}
		},
		[tabs, writeFileToSystem]
	);

	const saveAllTabs = useCallback(async () => {
		const dirtyTabs = tabs.filter((tab) => tab.isDirty);
		await Promise.all(dirtyTabs.map((tab) => saveTab(tab.id)));
	}, [tabs, saveTab]);

	const getActiveTab = useCallback((): EditorTab | null => {
		return tabs.find((tab) => tab.id === activeTabId) || null;
	}, [tabs, activeTabId]);

	// Simplified for web version - keyboard shortcuts handled in App component

	const value: EditorContextType = {
		tabs,
		activeTabId,
		openFile,
		closeTab,
		setActiveTab,
		updateTabContent,
		saveTab,
		saveAllTabs,
		getActiveTab,
	};

	return (
		<EditorContext.Provider value={value}>{children}</EditorContext.Provider>
	);
};

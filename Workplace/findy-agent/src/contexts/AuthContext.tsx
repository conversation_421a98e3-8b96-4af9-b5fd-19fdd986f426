import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { Amplify } from "aws-amplify";
import {
  signIn,
  signUp,
  signOut,
  getCurrentUser,
  fetchAuthSession,
} from "aws-amplify/auth";
import { amplifyConfig } from "../config/aws-config";

// Configure Amplify
Amplify.configure(amplifyConfig);

export interface User {
  id: string;
  email: string;
  givenName: string;
  familyName: string;
}

export interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  signup: (
    email: string,
    password: string,
    givenName: string,
    familyName: string
  ) => Promise<void>;
  logout: () => Promise<void>;
  getAuthToken: () => Promise<string | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for existing session on app load
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      // Check if user is authenticated with Cognito
      const currentUser = await getCurrentUser();

      if (currentUser) {
        const userData: User = {
          id: currentUser.userId,
          email: currentUser.signInDetails?.loginId || "",
          givenName:
            currentUser.signInDetails?.loginId?.split("@")[0] || "User",
          familyName: "User",
        };
        setUser(userData);
      }
    } catch (error) {
      console.log("No authenticated user found:", error);
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      // Sign in with AWS Cognito
      const signInResult = await signIn({
        username: email,
        password: password,
      });

      if (signInResult.isSignedIn) {
        // Get the current user after successful sign in
        const currentUser = await getCurrentUser();
        const userData: User = {
          id: currentUser.userId,
          email: email,
          givenName: email.split("@")[0],
          familyName: "User",
        };
        setUser(userData);
      } else {
        throw new Error("Sign in was not completed");
      }
    } catch (error: any) {
      console.error("Login error:", error);
      throw new Error(error.message || "Login failed");
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (
    email: string,
    password: string,
    givenName: string,
    familyName: string
  ) => {
    setIsLoading(true);
    try {
      // Sign up with AWS Cognito
      const signUpResult = await signUp({
        username: email,
        password: password,
        options: {
          userAttributes: {
            email: email,
            given_name: givenName,
            family_name: familyName,
          },
        },
      });

      if (signUpResult.isSignUpComplete) {
        // If signup is complete, sign in the user
        await login(email, password);
      } else {
        // Handle confirmation step if needed
        throw new Error("Please check your email for verification code");
      }
    } catch (error: any) {
      console.error("Signup error:", error);
      throw new Error(error.message || "Signup failed");
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await signOut();
      setUser(null);
    } catch (error) {
      console.error("Logout error:", error);
      throw error;
    }
  };

  const getAuthToken = async () => {
    try {
      const session = await fetchAuthSession();
      return session.tokens?.idToken?.toString() || null;
    } catch (error) {
      console.error("Error getting auth token:", error);
      return null;
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    signup,
    logout,
    getAuthToken,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

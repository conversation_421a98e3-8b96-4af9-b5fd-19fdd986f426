import React, {
	createContext,
	useContext,
	useState,
	useCallback,
	ReactNode,
} from "react";
import { v4 as uuidv4 } from "uuid";

export interface Terminal {
	id: string;
	name: string;
	isActive: boolean;
	cwd: string;
}

interface TerminalContextType {
	terminals: Terminal[];
	activeTerminalId: string | null;
	createTerminal: (name?: string, cwd?: string) => Promise<string>;
	closeTerminal: (terminalId: string) => void;
	setActiveTerminal: (terminalId: string) => void;
	writeToTerminal: (terminalId: string, data: string) => void;
	resizeTerminal: (terminalId: string, cols: number, rows: number) => void;
}

const TerminalContext = createContext<TerminalContextType | undefined>(
	undefined
);

export const useTerminal = () => {
	const context = useContext(TerminalContext);
	if (context === undefined) {
		throw new Error("useTerminal must be used within a TerminalProvider");
	}
	return context;
};

interface TerminalProviderProps {
	children: ReactNode;
}

export const TerminalProvider: React.FC<TerminalProviderProps> = ({
	children,
}) => {
	const [terminals, setTerminals] = useState<Terminal[]>([]);
	const [activeTerminalId, setActiveTerminalId] = useState<string | null>(null);

	const createTerminal = useCallback(
		async (name?: string, cwd?: string): Promise<string> => {
			const terminalId = uuidv4();
			const terminalName = name || `Terminal ${terminals.length + 1}`;

			// Mock terminal creation for web version
			const newTerminal: Terminal = {
				id: terminalId,
				name: terminalName,
				isActive: true,
				cwd: cwd || "/mock/workspace",
			};

			setTerminals((prev) => {
				const updated = prev.map((t) => ({ ...t, isActive: false }));
				return [...updated, newTerminal];
			});

			setActiveTerminalId(terminalId);
			return terminalId;
		},
		[terminals.length]
	);

	const closeTerminal = useCallback(
		(terminalId: string) => {
			// Mock terminal close for web version
			setTerminals((prev) => {
				const newTerminals = prev.filter((t) => t.id !== terminalId);

				// If closing the active terminal, set a new active terminal
				if (activeTerminalId === terminalId) {
					if (newTerminals.length > 0) {
						const newActiveTerminal = newTerminals[newTerminals.length - 1];
						newActiveTerminal.isActive = true;
						setActiveTerminalId(newActiveTerminal.id);
					} else {
						setActiveTerminalId(null);
					}
				}

				return newTerminals;
			});
		},
		[activeTerminalId]
	);

	const setActiveTerminal = useCallback((terminalId: string) => {
		setTerminals((prev) =>
			prev.map((t) => ({
				...t,
				isActive: t.id === terminalId,
			}))
		);
		setActiveTerminalId(terminalId);
	}, []);

	const writeToTerminal = useCallback((terminalId: string, data: string) => {
		// Mock terminal write for web version
		console.log(`Writing to terminal ${terminalId}:`, data);
	}, []);

	const resizeTerminal = useCallback(
		(terminalId: string, cols: number, rows: number) => {
			// Mock terminal resize for web version
			console.log(`Resizing terminal ${terminalId} to ${cols}x${rows}`);
		},
		[]
	);

	// No Electron events in web version

	const value: TerminalContextType = {
		terminals,
		activeTerminalId,
		createTerminal,
		closeTerminal,
		setActiveTerminal,
		writeToTerminal,
		resizeTerminal,
	};

	return (
		<TerminalContext.Provider value={value}>
			{children}
		</TerminalContext.Provider>
	);
};

import React, {
	createContext,
	useContext,
	useState,
	ReactNode,
	useCallback,
} from "react";

export interface FileSystemItem {
	name: string;
	path: string;
	isDirectory: boolean;
	size: number;
	modified: Date;
	handle?: FileSystemFileHandle | FileSystemDirectoryHandle;
	children?: FileSystemItem[];
	isExpanded?: boolean;
}

interface FileSystemContextType {
	currentWorkspace: string | null;
	workspaceHandle: FileSystemDirectoryHandle | null;
	setCurrentWorkspace: (
		workspace: string | null,
		handle?: FileSystemDirectoryHandle
	) => void;
	recentWorkspaces: string[];
	addRecentWorkspace: (workspace: string) => void;
	openDirectory: () => Promise<void>;
	readFile: (handle: FileSystemFileHandle) => Promise<string>;
	writeFile: (handle: FileSystemFileHandle, content: string) => Promise<void>;
	createFile: (
		dirHandle: FileSystemDirectoryHandle,
		name: string,
		content?: string
	) => Promise<FileSystemFileHandle>;
	deleteFile: (
		dirHandle: FileSystemDirectoryHandle,
		name: string
	) => Promise<void>;
	readDirectory: (
		handle: FileSystemDirectoryHandle
	) => Promise<FileSystemItem[]>;
	supportsFileSystemAccess: boolean;
}

const FileSystemContext = createContext<FileSystemContextType | undefined>(
	undefined
);

export const useFileSystem = () => {
	const context = useContext(FileSystemContext);
	if (context === undefined) {
		throw new Error("useFileSystem must be used within a FileSystemProvider");
	}
	return context;
};

interface FileSystemProviderProps {
	children: ReactNode;
}

export const FileSystemProvider: React.FC<FileSystemProviderProps> = ({
	children,
}) => {
	const [currentWorkspace, setCurrentWorkspaceState] = useState<string | null>(
		null
	);
	const [workspaceHandle, setWorkspaceHandle] =
		useState<FileSystemDirectoryHandle | null>(null);
	const [recentWorkspaces, setRecentWorkspaces] = useState<string[]>([]);

	// Check if File System Access API is supported
	const supportsFileSystemAccess = "showDirectoryPicker" in window;

	const addRecentWorkspace = useCallback((workspace: string) => {
		setRecentWorkspaces((prev) => {
			const filtered = prev.filter((w) => w !== workspace);
			return [workspace, ...filtered].slice(0, 10);
		});
	}, []);

	const setCurrentWorkspace = useCallback(
		(workspace: string | null, handle?: FileSystemDirectoryHandle) => {
			setCurrentWorkspaceState(workspace);
			setWorkspaceHandle(handle || null);
			if (workspace) {
				addRecentWorkspace(workspace);
			}
		},
		[addRecentWorkspace]
	);

	const openDirectory = useCallback(async () => {
		if (!supportsFileSystemAccess) {
			alert(
				"File System Access API is not supported in this browser. Please use Chrome/Edge 86+ or enable the feature flag."
			);
			return;
		}

		try {
			const dirHandle = await window.showDirectoryPicker();
			setCurrentWorkspace(dirHandle.name, dirHandle);
		} catch (error) {
			if ((error as Error).name !== "AbortError") {
				console.error("Error opening directory:", error);
			}
		}
	}, [supportsFileSystemAccess, setCurrentWorkspace]);

	const readFile = useCallback(
		async (handle: FileSystemFileHandle): Promise<string> => {
			const file = await handle.getFile();
			return await file.text();
		},
		[]
	);

	const writeFile = useCallback(
		async (handle: FileSystemFileHandle, content: string): Promise<void> => {
			const writable = await handle.createWritable();
			await writable.write(content);
			await writable.close();
		},
		[]
	);

	const createFile = useCallback(
		async (
			dirHandle: FileSystemDirectoryHandle,
			name: string,
			content: string = ""
		): Promise<FileSystemFileHandle> => {
			const fileHandle = await dirHandle.getFileHandle(name, { create: true });
			if (content) {
				await writeFile(fileHandle, content);
			}
			return fileHandle;
		},
		[writeFile]
	);

	const deleteFile = useCallback(
		async (
			dirHandle: FileSystemDirectoryHandle,
			name: string
		): Promise<void> => {
			await dirHandle.removeEntry(name);
		},
		[]
	);

	const readDirectory = useCallback(
		async (handle: FileSystemDirectoryHandle): Promise<FileSystemItem[]> => {
			const items: FileSystemItem[] = [];

			for await (const [name, entryHandle] of handle.entries()) {
				const isDirectory = entryHandle.kind === "directory";
				let size = 0;
				let modified = new Date();

				if (!isDirectory) {
					try {
						const file = await (entryHandle as FileSystemFileHandle).getFile();
						size = file.size;
						modified = new Date(file.lastModified);
					} catch (error) {
						console.warn("Could not read file stats:", error);
					}
				}

				items.push({
					name,
					path: `${handle.name}/${name}`,
					isDirectory,
					size,
					modified,
					handle: entryHandle,
					children: isDirectory ? [] : undefined,
					isExpanded: false,
				});
			}

			// Sort: directories first, then files, both alphabetically
			return items.sort((a, b) => {
				if (a.isDirectory && !b.isDirectory) return -1;
				if (!a.isDirectory && b.isDirectory) return 1;
				return a.name.localeCompare(b.name);
			});
		},
		[]
	);

	const value: FileSystemContextType = {
		currentWorkspace,
		workspaceHandle,
		setCurrentWorkspace,
		recentWorkspaces,
		addRecentWorkspace,
		openDirectory,
		readFile,
		writeFile,
		createFile,
		deleteFile,
		readDirectory,
		supportsFileSystemAccess,
	};

	return (
		<FileSystemContext.Provider value={value}>
			{children}
		</FileSystemContext.Provider>
	);
};

// Extend Window interface for File System Access API
declare global {
	interface Window {
		showDirectoryPicker(): Promise<FileSystemDirectoryHandle>;
		showOpenFilePicker(options?: any): Promise<FileSystemFileHandle[]>;
		showSaveFilePicker(options?: any): Promise<FileSystemFileHandle>;
	}

	// Add File System Access API interfaces
	interface FileSystemWritableFileStream extends WritableStream {
		write(data: string | BufferSource | Blob): Promise<void>;
		close(): Promise<void>;
	}

	interface FileSystemFileHandle {
		createWritable(): Promise<FileSystemWritableFileStream>;
		getFile(): Promise<File>;
	}

	interface FileSystemDirectoryHandle {
		entries(): AsyncIterableIterator<[string, FileSystemFileHandle | FileSystemDirectoryHandle]>;
		getFileHandle(name: string, options?: { create?: boolean }): Promise<FileSystemFileHandle>;
		getDirectoryHandle(name: string, options?: { create?: boolean }): Promise<FileSystemDirectoryHandle>;
		removeEntry(name: string, options?: { recursive?: boolean }): Promise<void>;
		readonly name: string;
		readonly kind: "directory";
	}

	interface FileSystemHandle {
		readonly kind: "file" | "directory";
		readonly name: string;
	}
}

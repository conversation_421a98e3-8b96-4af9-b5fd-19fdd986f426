import React, { useState, useCallback } from "react";
import styled, { ThemeProvider, createGlobalStyle } from "styled-components";
import ChatPanel from "./components/ChatPanel";
import ActionPanel from "./components/ActionPanel";
import ResourcePanel from "./components/ResourcePanel";
import WelcomeScreen from "./components/WelcomeScreen";
import TaskBar from "./components/TaskBar";
import { FileSystemProvider } from "./contexts/FileSystemContext";
import { EditorProvider } from "./contexts/EditorContext";
import { AuthProvider } from "./contexts/AuthContext";
import { darkTheme, lightTheme } from "./themes";

const GlobalStyle = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow: hidden;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    color: rgba(255, 255, 255, 0.9);
  }

  #root {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
  }

  @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap');
`;

const AppContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: rgba(255, 255, 255, 0.9);
  position: relative;
`;

const MainContent = styled.div<{ hasActiveTasks: boolean }>`
  display: flex;
  flex: 1;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: ${(props) =>
    props.hasActiveTasks ? "rgba(255, 255, 255, 0.02)" : "transparent"};
`;

const PanelContainer = styled.div<{
  isMinimized?: boolean;
  width?: string;
  showActionPanel: boolean;
}>`
  display: flex;
  flex-direction: column;
  width: ${(props) => {
    if (props.isMinimized) return "60px";
    if (props.showActionPanel) return props.width || "320px";
    return props.width || "400px";
  }};
  background: rgba(255, 255, 255, 0.02);
  border-right: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 10;
`;

const ContentArea = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.01);
`;

const ActionPanelContainer = styled.div<{ isVisible: boolean }>`
  display: flex;
  flex-direction: column;
  width: ${(props) => (props.isVisible ? "500px" : "0px")};
  background: rgba(255, 255, 255, 0.02);
  border-right: 1px solid rgba(255, 255, 255, 0.08);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  z-index: 5;
`;

const ResourcePanelContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 280px;
  background: rgba(255, 255, 255, 0.02);
  border-left: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
`;

export interface Task {
  id: string;
  title: string;
  description: string;
  status: "active" | "completed" | "paused";
  createdAt: Date;
  progress?: number;
}

function App() {
  const [isDarkTheme, setIsDarkTheme] = useState(true);
  const [tasks, setTasks] = useState<Task[]>([]);
  const [activeTask, setActiveTask] = useState<Task | null>(null);
  const [isChatMinimized, setIsChatMinimized] = useState(false);
  const [showActionPanel, setShowActionPanel] = useState(false);
  const [agentActions, setAgentActions] = useState<any[]>([]);

  const theme = isDarkTheme ? darkTheme : lightTheme;

  const createTask = useCallback((title: string, description: string) => {
    const newTask: Task = {
      id: Date.now().toString(),
      title,
      description,
      status: "active",
      createdAt: new Date(),
      progress: 0,
    };
    setTasks((prev) => [...prev, newTask]);
    setActiveTask(newTask);
    setShowActionPanel(true);
    setIsChatMinimized(true);
  }, []);

  const selectTask = useCallback((task: Task) => {
    setActiveTask(task);
    if (task.status === "active") {
      setShowActionPanel(true);
      setIsChatMinimized(true);
    }
  }, []);

  const toggleChatPanel = useCallback(() => {
    setIsChatMinimized((prev) => !prev);
    if (isChatMinimized && showActionPanel) {
      setShowActionPanel(false);
    }
  }, [isChatMinimized, showActionPanel]);

  const onAgentAction = useCallback((action: any) => {
    setAgentActions((prev) => [...prev, { ...action, timestamp: new Date() }]);
  }, []);

  const hasActiveTasks = tasks.length > 0;

  return (
    <ThemeProvider theme={theme}>
      <GlobalStyle />
      <AuthProvider>
        <FileSystemProvider>
          <EditorProvider>
            <AppContainer>
              {hasActiveTasks && (
                <TaskBar
                  tasks={tasks}
                  activeTask={activeTask}
                  onTaskSelect={selectTask}
                  onNewTask={() => setShowActionPanel(false)}
                  isDarkTheme={isDarkTheme}
                  onToggleTheme={() => setIsDarkTheme((prev) => !prev)}
                />
              )}
              <MainContent hasActiveTasks={hasActiveTasks}>
                {!hasActiveTasks ? (
                  <WelcomeScreen onCreateTask={createTask} />
                ) : (
                  <>
                    <PanelContainer
                      isMinimized={isChatMinimized}
                      showActionPanel={showActionPanel}
                    >
                      <ChatPanel
                        isMinimized={isChatMinimized}
                        onToggle={toggleChatPanel}
                        activeTask={activeTask}
                        onCreateTask={createTask}
                        onAgentAction={onAgentAction}
                      />
                    </PanelContainer>
                    <ActionPanelContainer isVisible={showActionPanel}>
                      <ActionPanel
                        isVisible={showActionPanel}
                        actions={agentActions}
                        activeTask={activeTask}
                        onClose={() => {
                          setShowActionPanel(false);
                          setIsChatMinimized(false);
                        }}
                      />
                    </ActionPanelContainer>
                    <ContentArea>
                      {/* Main content area for code editor, etc. */}
                    </ContentArea>
                    <ResourcePanelContainer>
                      <ResourcePanel
                        activeTask={activeTask}
                        onToggleTheme={() => setIsDarkTheme((prev) => !prev)}
                        isDarkTheme={isDarkTheme}
                      />
                    </ResourcePanelContainer>
                  </>
                )}
              </MainContent>
            </AppContainer>
          </EditorProvider>
        </FileSystemProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;

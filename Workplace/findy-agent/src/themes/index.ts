export interface Theme {
  colors: {
    background: string;
    foreground: string;
    primary: string;
    secondary: string;
    accent: string;
    border: string;
    hover: string;
    active: string;
    inactive: string;
    error: string;
    warning: string;
    success: string;
    info: string;
    
    // Activity Bar
    activityBarBackground: string;
    activityBarForeground: string;
    activityBarBorder: string;
    activityBarActiveBorder: string;
    activityBarInactiveForeground: string;
    
    // Sidebar
    sidebarBackground: string;
    sidebarForeground: string;
    sidebarBorder: string;
    
    // Editor
    editorBackground: string;
    editorForeground: string;
    editorLineHighlight: string;
    editorSelection: string;
    editorCursor: string;
    
    // Tabs
    tabActiveBackground: string;
    tabActiveForeground: string;
    tabInactiveBackground: string;
    tabInactiveForeground: string;
    tabBorder: string;
    tabActiveBorder: string;
    
    // Panel
    panelBackground: string;
    panelForeground: string;
    panelBorder: string;
    
    // Terminal
    terminalBackground: string;
    terminalForeground: string;
    terminalCursor: string;
    terminalSelection: string;
    
    // Status Bar
    statusBarBackground: string;
    statusBarForeground: string;
    statusBarBorder: string;
    
    // Input
    inputBackground: string;
    inputForeground: string;
    inputBorder: string;
    inputFocusBorder: string;
    
    // Button
    buttonBackground: string;
    buttonForeground: string;
    buttonHoverBackground: string;
    
    // List
    listActiveSelectionBackground: string;
    listActiveSelectionForeground: string;
    listInactiveSelectionBackground: string;
    listInactiveSelectionForeground: string;
    listHoverBackground: string;
    listHoverForeground: string;
  };
  
  fonts: {
    primary: string;
    monospace: string;
  };
  
  sizes: {
    activityBarWidth: string;
    statusBarHeight: string;
    tabHeight: string;
    borderWidth: string;
  };
}

export const darkTheme: Theme = {
  colors: {
    background: '#1e1e1e',
    foreground: '#cccccc',
    primary: '#007acc',
    secondary: '#2d2d30',
    accent: '#0e639c',
    border: '#3c3c3c',
    hover: '#2a2d2e',
    active: '#094771',
    inactive: '#68217a',
    error: '#f14c4c',
    warning: '#ffcc02',
    success: '#89d185',
    info: '#75beff',
    
    // Activity Bar
    activityBarBackground: '#333333',
    activityBarForeground: '#ffffff',
    activityBarBorder: '#3c3c3c',
    activityBarActiveBorder: '#007acc',
    activityBarInactiveForeground: '#cccccc',
    
    // Sidebar
    sidebarBackground: '#252526',
    sidebarForeground: '#cccccc',
    sidebarBorder: '#3c3c3c',
    
    // Editor
    editorBackground: '#1e1e1e',
    editorForeground: '#d4d4d4',
    editorLineHighlight: '#2a2d2e',
    editorSelection: '#264f78',
    editorCursor: '#ffffff',
    
    // Tabs
    tabActiveBackground: '#1e1e1e',
    tabActiveForeground: '#ffffff',
    tabInactiveBackground: '#2d2d30',
    tabInactiveForeground: '#969696',
    tabBorder: '#3c3c3c',
    tabActiveBorder: '#007acc',
    
    // Panel
    panelBackground: '#1e1e1e',
    panelForeground: '#cccccc',
    panelBorder: '#3c3c3c',
    
    // Terminal
    terminalBackground: '#1e1e1e',
    terminalForeground: '#cccccc',
    terminalCursor: '#ffffff',
    terminalSelection: '#264f78',
    
    // Status Bar
    statusBarBackground: '#007acc',
    statusBarForeground: '#ffffff',
    statusBarBorder: '#3c3c3c',
    
    // Input
    inputBackground: '#3c3c3c',
    inputForeground: '#cccccc',
    inputBorder: '#3c3c3c',
    inputFocusBorder: '#007acc',
    
    // Button
    buttonBackground: '#0e639c',
    buttonForeground: '#ffffff',
    buttonHoverBackground: '#1177bb',
    
    // List
    listActiveSelectionBackground: '#094771',
    listActiveSelectionForeground: '#ffffff',
    listInactiveSelectionBackground: '#37373d',
    listInactiveSelectionForeground: '#cccccc',
    listHoverBackground: '#2a2d2e',
    listHoverForeground: '#cccccc',
  },
  
  fonts: {
    primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',
    monospace: '"SF Mono", Monaco, Inconsolata, "Roboto Mono", "Source Code Pro", Menlo, Consolas, "DejaVu Sans Mono", monospace',
  },
  
  sizes: {
    activityBarWidth: '48px',
    statusBarHeight: '22px',
    tabHeight: '35px',
    borderWidth: '1px',
  },
};

export const lightTheme: Theme = {
  colors: {
    background: '#ffffff',
    foreground: '#333333',
    primary: '#005a9e',
    secondary: '#f3f3f3',
    accent: '#0066cc',
    border: '#e5e5e5',
    hover: '#f0f0f0',
    active: '#e4e6f1',
    inactive: '#ccc2dc',
    error: '#e51400',
    warning: '#bf8803',
    success: '#388a34',
    info: '#1a85ff',
    
    // Activity Bar
    activityBarBackground: '#2c2c2c',
    activityBarForeground: '#ffffff',
    activityBarBorder: '#e5e5e5',
    activityBarActiveBorder: '#005a9e',
    activityBarInactiveForeground: '#cccccc',
    
    // Sidebar
    sidebarBackground: '#f3f3f3',
    sidebarForeground: '#333333',
    sidebarBorder: '#e5e5e5',
    
    // Editor
    editorBackground: '#ffffff',
    editorForeground: '#333333',
    editorLineHighlight: '#f0f0f0',
    editorSelection: '#add6ff',
    editorCursor: '#000000',
    
    // Tabs
    tabActiveBackground: '#ffffff',
    tabActiveForeground: '#333333',
    tabInactiveBackground: '#ececec',
    tabInactiveForeground: '#6f6f6f',
    tabBorder: '#e5e5e5',
    tabActiveBorder: '#005a9e',
    
    // Panel
    panelBackground: '#ffffff',
    panelForeground: '#333333',
    panelBorder: '#e5e5e5',
    
    // Terminal
    terminalBackground: '#ffffff',
    terminalForeground: '#333333',
    terminalCursor: '#000000',
    terminalSelection: '#add6ff',
    
    // Status Bar
    statusBarBackground: '#005a9e',
    statusBarForeground: '#ffffff',
    statusBarBorder: '#e5e5e5',
    
    // Input
    inputBackground: '#ffffff',
    inputForeground: '#333333',
    inputBorder: '#e5e5e5',
    inputFocusBorder: '#005a9e',
    
    // Button
    buttonBackground: '#0066cc',
    buttonForeground: '#ffffff',
    buttonHoverBackground: '#005a9e',
    
    // List
    listActiveSelectionBackground: '#e4e6f1',
    listActiveSelectionForeground: '#333333',
    listInactiveSelectionBackground: '#f0f0f0',
    listInactiveSelectionForeground: '#333333',
    listHoverBackground: '#f0f0f0',
    listHoverForeground: '#333333',
  },
  
  fonts: {
    primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',
    monospace: '"SF Mono", Monaco, Inconsolata, "Roboto Mono", "Source Code Pro", Menlo, Consolas, "DejaVu Sans Mono", monospace',
  },
  
  sizes: {
    activityBarWidth: '48px',
    statusBarHeight: '22px',
    tabHeight: '35px',
    borderWidth: '1px',
  },
};

import { useEffect } from 'react';
import { useEditor } from '../contexts/EditorContext';

interface KeyboardShortcutsProps {
  onToggleExplorer: () => void;
  onToggleTerminal: () => void;
}

const KeyboardShortcuts: React.FC<KeyboardShortcutsProps> = ({
  onToggleExplorer,
  onToggleTerminal,
}) => {
  const { saveTab, getActiveTab } = useEditor();

  useEffect(() => {
    const handleKeyDown = async (e: KeyboardEvent) => {
      // Ctrl/Cmd + Shift + E - Toggle Explorer
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'E') {
        e.preventDefault();
        onToggleExplorer();
      }
      
      // Ctrl/Cmd + ` - Toggle Terminal
      if ((e.metaKey || e.ctrlKey) && e.key === '`') {
        e.preventDefault();
        onToggleTerminal();
      }
      
      // Ctrl/Cmd + S - Save File
      if ((e.metaKey || e.ctrlKey) && e.key === 's') {
        e.preventDefault();
        const activeTab = getActiveTab();
        if (activeTab) {
          await saveTab(activeTab.id);
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onToggleExplorer, onToggleTerminal, saveTab, getActiveTab]);

  return null; // This component doesn't render anything
};

export default KeyboardShortcuts;

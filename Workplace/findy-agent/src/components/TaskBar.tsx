import React from "react";
import styled from "styled-components";
import { Task } from "../App";
import { UserProfile } from "./UserProfile";

const TaskBarContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.02);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  z-index: 100;
`;

const TaskList = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  overflow-x: auto;
  padding: 0 8px;

  &::-webkit-scrollbar {
    height: 3px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }
`;

const TaskTab = styled.div<{ isActive: boolean; status: string }>`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: ${(props) => {
    if (props.isActive) return "rgba(255, 255, 255, 0.1)";
    return "transparent";
  }};
  border: 1px solid
    ${(props) => {
      if (props.isActive) return "rgba(255, 255, 255, 0.2)";
      return "transparent";
    }};
  min-width: 160px;
  max-width: 220px;

  &:hover {
    background: rgba(255, 255, 255, 0.05);
  }
`;

const TaskStatus = styled.div<{ status: string }>`
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: ${(props) => {
    switch (props.status) {
      case "active":
        return "#00ff88";
      case "completed":
        return "#0099ff";
      case "paused":
        return "#ffaa00";
      default:
        return "rgba(255, 255, 255, 0.4)";
    }
  }};
  flex-shrink: 0;
`;

const TaskTitle = styled.span`
  font-size: 13px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  flex: 1;
`;

const TaskProgress = styled.div<{ progress: number }>`
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.05);
  padding: 2px 6px;
  border-radius: 3px;
  min-width: 35px;
  text-align: center;
  flex-shrink: 0;
`;

const Controls = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const ControlButton = styled.button`
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.15);
  }

  &:active {
    transform: translateY(1px);
  }
`;

const ThemeToggle = styled.button`
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;

  &:hover {
    background: rgba(255, 255, 255, 0.15);
  }

  &:active {
    transform: scale(0.95);
  }
`;

const Logo = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 20px;
  font-weight: 500;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
`;

const LogoIcon = styled.div`
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #000;
`;

interface TaskBarProps {
  tasks: Task[];
  activeTask: Task | null;
  onTaskSelect: (task: Task) => void;
  onNewTask: () => void;
  isDarkTheme: boolean;
  onToggleTheme: () => void;
}

const TaskBar: React.FC<TaskBarProps> = ({
  tasks,
  activeTask,
  onTaskSelect,
  onNewTask,
  isDarkTheme,
  onToggleTheme,
}) => {
  return (
    <TaskBarContainer>
      <Logo>
        <LogoIcon>SE</LogoIcon>
        Software Engineer
      </Logo>

      <TaskList>
        {tasks.map((task) => (
          <TaskTab
            key={task.id}
            isActive={activeTask?.id === task.id}
            status={task.status}
            onClick={() => onTaskSelect(task)}
          >
            <TaskStatus status={task.status} />
            <TaskTitle>{task.title}</TaskTitle>
            {task.progress !== undefined && (
              <TaskProgress progress={task.progress}>
                {Math.round(task.progress)}%
              </TaskProgress>
            )}
          </TaskTab>
        ))}
      </TaskList>

      <Controls>
        <UserProfile />
        <ControlButton onClick={onNewTask}>New Task</ControlButton>
        <ThemeToggle onClick={onToggleTheme}>
          {isDarkTheme ? "☀️" : "🌙"}
        </ThemeToggle>
      </Controls>
    </TaskBarContainer>
  );
};

export default TaskBar;

import React, { useState } from 'react';
import styled from 'styled-components';
import { useAuth } from '../../contexts/AuthContext';
import { useAuthenticatedApi } from '../../hooks/useAuthenticatedApi';
import { ProtectedRoute } from './ProtectedRoute';

const TestContainer = styled.div`
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const TestSection = styled.div`
  margin-bottom: 24px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
`;

const TestTitle = styled.h3`
  color: #00ff88;
  margin: 0 0 12px 0;
  font-size: 18px;
`;

const TestButton = styled.button`
  background: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
  color: #0f0f23;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  margin: 4px 8px 4px 0;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 255, 136, 0.3);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const TestResult = styled.div<{ success?: boolean; error?: boolean }>`
  margin-top: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  background: ${props => 
    props.success ? 'rgba(0, 255, 136, 0.1)' : 
    props.error ? 'rgba(255, 107, 107, 0.1)' : 
    'rgba(255, 255, 255, 0.05)'
  };
  color: ${props => 
    props.success ? '#00ff88' : 
    props.error ? '#ff6b6b' : 
    'rgba(255, 255, 255, 0.7)'
  };
  border: 1px solid ${props => 
    props.success ? 'rgba(0, 255, 136, 0.2)' : 
    props.error ? 'rgba(255, 107, 107, 0.2)' : 
    'rgba(255, 255, 255, 0.1)'
  };
`;

const UserInfo = styled.div`
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid rgba(0, 255, 136, 0.2);
  border-radius: 8px;
  padding: 12px;
  margin: 12px 0;
`;

export const AuthTest: React.FC = () => {
  const { user, isAuthenticated, isLoading, logout } = useAuth();
  const apiService = useAuthenticatedApi();
  const [testResults, setTestResults] = useState<Record<string, any>>({});

  const updateResult = (test: string, result: any) => {
    setTestResults(prev => ({ ...prev, [test]: result }));
  };

  const testGetTasks = async () => {
    try {
      updateResult('getTasks', { loading: true });
      const result = await apiService.getTasks();
      updateResult('getTasks', { success: true, data: result });
    } catch (error: any) {
      updateResult('getTasks', { error: true, message: error.message });
    }
  };

  const testCreateTask = async () => {
    try {
      updateResult('createTask', { loading: true });
      const result = await apiService.createTask(
        'Test Task',
        'This is a test task created from the auth test component'
      );
      updateResult('createTask', { success: true, data: result });
    } catch (error: any) {
      updateResult('createTask', { error: true, message: error.message });
    }
  };

  const testGetAuthToken = async () => {
    try {
      updateResult('getAuthToken', { loading: true });
      const token = await apiService['authContext'].getAuthToken();
      updateResult('getAuthToken', { 
        success: true, 
        data: token ? `Token: ${token.substring(0, 50)}...` : 'No token' 
      });
    } catch (error: any) {
      updateResult('getAuthToken', { error: true, message: error.message });
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      setTestResults({});
    } catch (error: any) {
      updateResult('logout', { error: true, message: error.message });
    }
  };

  const renderTestResult = (testName: string) => {
    const result = testResults[testName];
    if (!result) return null;

    if (result.loading) {
      return <TestResult>Loading...</TestResult>;
    }

    if (result.success) {
      return (
        <TestResult success>
          ✅ Success: {JSON.stringify(result.data, null, 2)}
        </TestResult>
      );
    }

    if (result.error) {
      return (
        <TestResult error>
          ❌ Error: {result.message}
        </TestResult>
      );
    }

    return null;
  };

  if (isLoading) {
    return (
      <TestContainer>
        <TestTitle>Loading authentication state...</TestTitle>
      </TestContainer>
    );
  }

  return (
    <TestContainer>
      <TestTitle>Authentication Test Dashboard</TestTitle>
      
      <TestSection>
        <TestTitle>Authentication Status</TestTitle>
        <div>
          <strong>Authenticated:</strong> {isAuthenticated ? '✅ Yes' : '❌ No'}
        </div>
        {user && (
          <UserInfo>
            <div><strong>User ID:</strong> {user.id}</div>
            <div><strong>Email:</strong> {user.email}</div>
            <div><strong>Name:</strong> {user.givenName} {user.familyName}</div>
          </UserInfo>
        )}
        {isAuthenticated && (
          <TestButton onClick={handleLogout}>
            Logout
          </TestButton>
        )}
      </TestSection>

      <ProtectedRoute
        fallback={
          <TestSection>
            <TestTitle>Protected Content</TestTitle>
            <TestResult error>
              ❌ This content is only available to authenticated users
            </TestResult>
          </TestSection>
        }
      >
        <TestSection>
          <TestTitle>API Tests (Protected)</TestTitle>
          
          <div>
            <TestButton onClick={testGetAuthToken}>
              Test Get Auth Token
            </TestButton>
            {renderTestResult('getAuthToken')}
          </div>

          <div>
            <TestButton onClick={testGetTasks}>
              Test Get Tasks
            </TestButton>
            {renderTestResult('getTasks')}
          </div>

          <div>
            <TestButton onClick={testCreateTask}>
              Test Create Task
            </TestButton>
            {renderTestResult('createTask')}
          </div>
        </TestSection>
      </ProtectedRoute>
    </TestContainer>
  );
};

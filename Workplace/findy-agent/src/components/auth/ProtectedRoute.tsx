import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { LoginModal } from './LoginModal';
import styled from 'styled-components';

const ProtectedContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: rgba(255, 255, 255, 0.9);
`;

const LoadingSpinner = styled.div`
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #00ff88;
  border-radius: 50%;
  animation: spin 1s linear infinite;

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const AuthPrompt = styled.div`
  text-align: center;
  padding: 32px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
`;

const AuthTitle = styled.h2`
  margin: 0 0 16px 0;
  font-size: 24px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
`;

const AuthMessage = styled.p`
  margin: 0 0 24px 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
`;

const SignInButton = styled.button`
  background: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
  color: #0f0f23;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 136, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
`;

interface ProtectedRouteProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  fallback 
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const [showLoginModal, setShowLoginModal] = React.useState(false);

  if (isLoading) {
    return (
      <ProtectedContainer>
        <LoadingSpinner />
      </ProtectedContainer>
    );
  }

  if (!isAuthenticated) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <ProtectedContainer>
        <AuthPrompt>
          <AuthTitle>Authentication Required</AuthTitle>
          <AuthMessage>
            You need to sign in to access this feature.
          </AuthMessage>
          <SignInButton onClick={() => setShowLoginModal(true)}>
            Sign In
          </SignInButton>
        </AuthPrompt>
        
        {showLoginModal && (
          <LoginModal
            isOpen={showLoginModal}
            onClose={() => setShowLoginModal(false)}
          />
        )}
      </ProtectedContainer>
    );
  }

  return <>{children}</>;
};

import React, { useCallback } from 'react';
import styled from 'styled-components';
import { useEditor } from '../contexts/EditorContext';

const TabBarContainer = styled.div`
  display: flex;
  background-color: ${props => props.theme.colors.tabInactiveBackground};
  border-bottom: ${props => props.theme.sizes.borderWidth} solid ${props => props.theme.colors.tabBorder};
  overflow-x: auto;
  overflow-y: hidden;
  height: ${props => props.theme.sizes.tabHeight};
  
  &::-webkit-scrollbar {
    height: 0;
  }
`;

const Tab = styled.div<{ isActive: boolean; isDirty: boolean }>`
  display: flex;
  align-items: center;
  padding: 0 12px;
  min-width: 120px;
  max-width: 200px;
  height: 100%;
  background-color: ${props => props.isActive 
    ? props.theme.colors.tabActiveBackground 
    : props.theme.colors.tabInactiveBackground};
  color: ${props => props.isActive 
    ? props.theme.colors.tabActiveForeground 
    : props.theme.colors.tabInactiveForeground};
  border-right: ${props => props.theme.sizes.borderWidth} solid ${props => props.theme.colors.tabBorder};
  border-top: 2px solid ${props => props.isActive 
    ? props.theme.colors.tabActiveBorder 
    : 'transparent'};
  cursor: pointer;
  user-select: none;
  position: relative;
  
  &:hover {
    background-color: ${props => props.isActive 
      ? props.theme.colors.tabActiveBackground 
      : props.theme.colors.hover};
  }
`;

const TabIcon = styled.span`
  margin-right: 6px;
  font-size: 12px;
  
  &::before {
    content: '📄';
  }
`;

const TabLabel = styled.span`
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 13px;
`;

const DirtyIndicator = styled.span<{ isDirty: boolean }>`
  margin-left: 4px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: ${props => props.isDirty 
    ? props.theme.colors.editorForeground 
    : 'transparent'};
  flex-shrink: 0;
`;

const CloseButton = styled.button<{ isDirty: boolean }>`
  background: none;
  border: none;
  color: ${props => props.theme.colors.tabInactiveForeground};
  cursor: pointer;
  padding: 2px;
  margin-left: 6px;
  border-radius: 2px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  flex-shrink: 0;
  opacity: ${props => props.isDirty ? 0 : 0.7};
  
  &:hover {
    background-color: ${props => props.theme.colors.hover};
    color: ${props => props.theme.colors.tabActiveForeground};
    opacity: 1;
  }
  
  &::before {
    content: '×';
    font-weight: bold;
  }
`;

const TabBar: React.FC = () => {
  const { tabs, activeTabId, setActiveTab, closeTab } = useEditor();

  const handleTabClick = useCallback((tabId: string) => {
    setActiveTab(tabId);
  }, [setActiveTab]);

  const handleCloseTab = useCallback((e: React.MouseEvent, tabId: string) => {
    e.stopPropagation();
    closeTab(tabId);
  }, [closeTab]);

  const handleMiddleClick = useCallback((e: React.MouseEvent, tabId: string) => {
    if (e.button === 1) { // Middle mouse button
      e.preventDefault();
      closeTab(tabId);
    }
  }, [closeTab]);

  if (tabs.length === 0) {
    return null;
  }

  return (
    <TabBarContainer>
      {tabs.map((tab) => (
        <Tab
          key={tab.id}
          isActive={tab.id === activeTabId}
          isDirty={tab.isDirty}
          onClick={() => handleTabClick(tab.id)}
          onMouseDown={(e) => handleMiddleClick(e, tab.id)}
          title={tab.filePath || tab.fileName}
        >
          <TabIcon />
          <TabLabel>{tab.fileName}</TabLabel>
          {tab.isDirty ? (
            <DirtyIndicator isDirty={true} />
          ) : (
            <CloseButton
              isDirty={false}
              onClick={(e) => handleCloseTab(e, tab.id)}
              title="Close"
            />
          )}
        </Tab>
      ))}
    </TabBarContainer>
  );
};

export default TabBar;

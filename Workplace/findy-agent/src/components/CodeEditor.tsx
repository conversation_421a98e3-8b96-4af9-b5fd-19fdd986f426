import React, { useRef, useEffect, useCallback } from "react";
import styled from "styled-components";
import Editor, { OnMount, OnChange } from "@monaco-editor/react";
import { useEditor, EditorTab } from "../contexts/EditorContext";

const EditorContainer = styled.div`
	flex: 1;
	background-color: ${(props) => props.theme.colors.editorBackground};
	overflow: hidden;
`;

interface CodeEditorProps {
	tab: EditorTab;
}

const CodeEditor: React.FC<CodeEditorProps> = ({ tab }) => {
	const { updateTabContent } = useEditor();
	const editorRef = useRef<any>(null);

	const handleEditorDidMount: OnMount = useCallback((editor, monaco) => {
		editorRef.current = editor;

		// Configure Monaco Editor theme
		monaco.editor.defineTheme("vs-dark-custom", {
			base: "vs-dark",
			inherit: true,
			rules: [],
			colors: {
				"editor.background": "#1e1e1e",
				"editor.foreground": "#d4d4d4",
				"editor.lineHighlightBackground": "#2a2d2e",
				"editor.selectionBackground": "#264f78",
				"editorCursor.foreground": "#ffffff",
				"editorLineNumber.foreground": "#858585",
				"editorLineNumber.activeForeground": "#c6c6c6",
				"editor.selectionHighlightBackground": "#add6ff26",
				"editor.wordHighlightBackground": "#575757b8",
				"editor.wordHighlightStrongBackground": "#004972b8",
				"editorBracketMatch.background": "#0064001a",
				"editorBracketMatch.border": "#888888",
			},
		});

		monaco.editor.defineTheme("vs-light-custom", {
			base: "vs",
			inherit: true,
			rules: [],
			colors: {
				"editor.background": "#ffffff",
				"editor.foreground": "#333333",
				"editor.lineHighlightBackground": "#f0f0f0",
				"editor.selectionBackground": "#add6ff",
				"editorCursor.foreground": "#000000",
				"editorLineNumber.foreground": "#237893",
				"editorLineNumber.activeForeground": "#0b216f",
				"editor.selectionHighlightBackground": "#add6ff26",
				"editor.wordHighlightBackground": "#57575740",
				"editor.wordHighlightStrongBackground": "#00497240",
				"editorBracketMatch.background": "#0064001a",
				"editorBracketMatch.border": "#888888",
			},
		});

		// Set the theme based on current theme
		monaco.editor.setTheme("vs-dark-custom");

		// Configure editor options
		editor.updateOptions({
			fontSize: 14,
			fontFamily:
				'"SF Mono", Monaco, Inconsolata, "Roboto Mono", "Source Code Pro", Menlo, Consolas, "DejaVu Sans Mono", monospace',
			lineHeight: 20,
			letterSpacing: 0.5,
			minimap: {
				enabled: true,
				side: "right",
				size: "proportional",
				showSlider: "mouseover",
			},
			scrollBeyondLastLine: false,
			wordWrap: "on",
			automaticLayout: true,
			tabSize: 2,
			insertSpaces: true,
			detectIndentation: true,
			renderWhitespace: "selection",
			renderControlCharacters: false,
			renderLineHighlight: "line",
			cursorBlinking: "blink",
			cursorSmoothCaretAnimation: "on",
			smoothScrolling: true,
			mouseWheelZoom: true,
			multiCursorModifier: "ctrlCmd",
			formatOnPaste: true,
			formatOnType: true,
			suggestOnTriggerCharacters: true,
			acceptSuggestionOnEnter: "on",
			snippetSuggestions: "top",
			wordBasedSuggestions: "matchingDocuments",
			bracketPairColorization: {
				enabled: true,
			},
			guides: {
				bracketPairs: true,
				indentation: true,
			},
			lightbulb: {
				enabled: true,
			},
		});

		// Add keyboard shortcuts
		editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
			// Save will be handled by the menu system
		});

		editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyF, () => {
			editor.getAction("actions.find")?.run();
		});

		editor.addCommand(
			monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyF,
			() => {
				editor.getAction("editor.action.startFindReplaceAction")?.run();
			}
		);

		// Focus the editor
		editor.focus();
	}, []);

	const handleEditorChange: OnChange = useCallback(
		(value) => {
			if (value !== undefined) {
				updateTabContent(tab.id, value);
			}
		},
		[tab.id, updateTabContent]
	);

	// Update editor content when tab changes
	useEffect(() => {
		if (editorRef.current && editorRef.current.getValue() !== tab.content) {
			editorRef.current.setValue(tab.content);
		}
	}, [tab.content]);

	return (
		<EditorContainer>
			<Editor
				height="100%"
				language={tab.language || "plaintext"}
				value={tab.content}
				onMount={handleEditorDidMount}
				onChange={handleEditorChange}
				options={{
					theme: "vs-dark-custom",
				}}
				loading={
					<div
						style={{
							display: "flex",
							alignItems: "center",
							justifyContent: "center",
							height: "100%",
							color: "#888",
							fontSize: "14px",
						}}
					>
						Loading editor...
					</div>
				}
			/>
		</EditorContainer>
	);
};

export default CodeEditor;

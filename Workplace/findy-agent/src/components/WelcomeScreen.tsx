import React, { useEffect, useState } from 'react';
import styled, { keyframes } from 'styled-components';
import { useAuth } from "../contexts/AuthContext";
import { LoginModal } from "./auth/LoginModal";

const fadeIn = keyframes`
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
`;

const glow = keyframes`
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3), 0 0 40px rgba(0, 255, 136, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(0, 255, 136, 0.5), 0 0 60px rgba(0, 255, 136, 0.2);
  }
`;

const lineGlow = keyframes`
  0%, 100% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.6;
  }
`;

const shimmer = keyframes`
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
`;

const appearDot = keyframes`
  0% {
    transform: scale(0) rotate(0deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.3) rotate(180deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) rotate(360deg);
    opacity: 1;
  }
`;

const WelcomeContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  position: relative;
  overflow: hidden;
  animation: ${fadeIn} 0.6s ease-out;
  cursor: crosshair;
`;

const InteractiveContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: auto;
  z-index: 1;
`;

const ClickDot = styled.div<{
  x: number;
  y: number;
  symbol: string;
  age: number;
}>`
  position: absolute;
  left: ${props => props.x}px;
  top: ${props => props.y}px;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, 
    rgba(0, 255, 136, ${props => Math.max(0.3, 1 - props.age * 0.1)}) 0%, 
    rgba(0, 153, 255, ${props => Math.max(0.2, 0.8 - props.age * 0.1)}) 100%
  );
  border: 2px solid rgba(0, 255, 136, ${props => Math.max(0.4, 1 - props.age * 0.1)});
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: rgba(0, 255, 136, 0.9);
  transform: translate(-50%, -50%);
  animation: ${appearDot} 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  backdrop-filter: blur(2px);
  box-shadow: 0 0 20px rgba(0, 255, 136, ${props => Math.max(0.2, 0.5 - props.age * 0.05)});
  cursor: pointer;
  pointer-events: auto;
  
  &:hover {
    transform: translate(-50%, -50%) scale(1.2);
    box-shadow: 0 0 30px rgba(0, 255, 136, 0.6);
    border-color: rgba(0, 255, 136, 1);
  }
  
  &:active {
    transform: translate(-50%, -50%) scale(1.1);
  }
  
  transition: all 0.3s ease;
  
  &::after {
    content: '${props => props.symbol}';
  }
`;

const ClickConnection = styled.div<{
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  opacity: number;
}>`
  position: absolute;
  left: ${props => props.x1}px;
  top: ${props => props.y1}px;
  width: ${props => Math.sqrt(Math.pow(props.x2 - props.x1, 2) + Math.pow(props.y2 - props.y1, 2))}px;
  height: 2px;
  background: linear-gradient(90deg, 
    rgba(0, 255, 136, ${props => props.opacity}) 0%, 
    rgba(0, 153, 255, ${props => props.opacity * 0.8}) 50%,
    rgba(0, 255, 136, ${props => props.opacity}) 100%
  );
  transform-origin: left center;
  transform: rotate(${props => Math.atan2(props.y2 - props.y1, props.x2 - props.x1) * 180 / Math.PI}deg);
  animation: ${lineGlow} 2s ease-in-out infinite;
  pointer-events: none;
`;

const InstructionText = styled.div`
  position: absolute;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  z-index: 5;
  pointer-events: none;
  transition: opacity 0.3s ease;
  
  @media (max-width: 768px) {
    font-size: 12px;
    top: 20px;
  }
`;

const ContentCard = styled.div`
  text-align: center;
  max-width: 450px;
  position: relative;
  z-index: 10;
  backdrop-filter: blur(10px);
  background: linear-gradient(135deg, 
    rgba(15, 15, 35, 0.8) 0%, 
    rgba(26, 26, 46, 0.6) 50%, 
    rgba(22, 33, 62, 0.8) 100%
  );
  border-radius: 20px;
  padding: 48px;
  border: 1px solid rgba(0, 255, 136, 0.2);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.05) inset,
    0 0 60px rgba(0, 255, 136, 0.1);
  pointer-events: auto;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 
      0 30px 60px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.08) inset,
      0 0 80px rgba(0, 255, 136, 0.15);
  }
  
  transition: all 0.4s ease;
`;

const LogoContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 32px;
`;

const Logo = styled.div`
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  animation: ${glow} 3s ease-in-out infinite;
  
  &::before {
    content: '';
    position: absolute;
    inset: 3px;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
    border-radius: 13px;
    z-index: 1;
  }
  
  &::after {
    content: 'F';
    position: relative;
    z-index: 2;
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
`;

const BrandName = styled.h1`
  font-size: 52px;
  font-weight: 700;
  background: linear-gradient(135deg, #00ff88 0%, #0099ff 50%, #00ff88 100%);
  background-size: 200% 100%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
  letter-spacing: -0.02em;
  animation: ${shimmer} 3s ease-in-out infinite;
`;

const Title = styled.h2`
  font-size: 28px;
  font-weight: 300;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 20px;
  line-height: 1.2;
  letter-spacing: -0.01em;
`;

const Subtitle = styled.p`
  font-size: 18px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 48px;
  line-height: 1.5;
  font-weight: 400;
`;

const StartButton = styled.button`
  background: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
  color: #0f0f23;
  border: none;
  border-radius: 12px;
  padding: 18px 36px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 255, 136, 0.3);
    
    &::before {
      left: 100%;
    }
  }
  
  &:active {
    transform: translateY(-1px);
  }
`;

interface WelcomeScreenProps {
  onCreateTask: (title: string, description: string) => void;
}

interface ClickDotType {
  id: number;
  x: number;
  y: number;
  symbol: string;
  timestamp: number;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onCreateTask }) => {
  const { isAuthenticated } = useAuth();
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [clickDots, setClickDots] = useState<ClickDotType[]>([]);
  const [showInstructions, setShowInstructions] = useState(true);

  const aiElements = [
    // AI & Tech Symbols
    '🤖', '🧠', '⚡', '💎', '🎯', '🚀',
    // Development Tools
    '⚙️', '🔧', '📊', '💻', '🌐', '🔮',
    // Tech Stack
    'JS', 'TS', 'PY', 'GO', 'AI', 'ML',
    // Process Flow
    '→', '↻', '◆', '▲', '●', '✓',
    // Advanced
    '∞', '⟲', '◇', '▼', '⬢', '⬡'
  ];

  // Calculate connections between click dots
  const connections = clickDots.reduce((acc, dot, i) => {
    clickDots.slice(i + 1).forEach((otherDot) => {
      const distance = Math.sqrt(
        Math.pow(otherDot.x - dot.x, 2) + Math.pow(otherDot.y - dot.y, 2)
      );
      if (distance < 200) { // Connect dots within 200px
        const maxAge = Math.max(
          Date.now() - dot.timestamp,
          Date.now() - otherDot.timestamp
        );
        const opacity = Math.max(0.1, 0.8 - (maxAge / 10000)); // Fade over 10 seconds

        acc.push({
          id: `${dot.id}-${otherDot.id}`,
          x1: dot.x,
          y1: dot.y,
          x2: otherDot.x,
          y2: otherDot.y,
          opacity
        });
      }
    });
    return acc;
  }, [] as Array<{ id: string, x1: number, y1: number, x2: number, y2: number, opacity: number }>);

  // Handle clicks to create dots
  const handleContainerClick = (e: React.MouseEvent) => {
    // Allow clicking anywhere on the container, not just empty space
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const newDot: ClickDotType = {
      id: Date.now(),
      x,
      y,
      symbol: aiElements[Math.floor(Math.random() * aiElements.length)],
      timestamp: Date.now()
    };

    setClickDots(prev => [...prev, newDot]);
    setShowInstructions(false);
  };

  // Handle clicking on existing dots
  const handleDotClick = (clickedDot: ClickDotType, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent container click

    // Create a burst effect - add multiple dots around the clicked dot
    const burstDots: ClickDotType[] = [];
    const numBurstDots = 4;
    const radius = 80;

    for (let i = 0; i < numBurstDots; i++) {
      const angle = (i / numBurstDots) * 2 * Math.PI;
      const x = clickedDot.x + Math.cos(angle) * radius;
      const y = clickedDot.y + Math.sin(angle) * radius;

      burstDots.push({
        id: Date.now() + i,
        x,
        y,
        symbol: aiElements[Math.floor(Math.random() * aiElements.length)],
        timestamp: Date.now()
      });
    }

    setClickDots(prev => [...prev, ...burstDots]);

    // Optional: refresh the clicked dot to make it "younger"
    setClickDots(prev => prev.map(dot =>
      dot.id === clickedDot.id
        ? { ...dot, timestamp: Date.now() }
        : dot
    ));
  };

  // Cleanup old dots
  useEffect(() => {
    const interval = setInterval(() => {
      setClickDots(prev =>
        prev.filter(dot => Date.now() - dot.timestamp < 15000) // Remove dots older than 15 seconds
      );
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleStart = () => {
    if (isAuthenticated) {
      onCreateTask('New Task', 'Software development task');
    } else {
      setShowLoginModal(true);
    }
  };

  return (
    <WelcomeContainer onClick={handleContainerClick}>
      {showInstructions && clickDots.length === 0 && (
        <InstructionText>
          Click anywhere to create AI network nodes • They'll connect automatically
        </InstructionText>
      )}

      <InteractiveContainer>
        {/* User-created dots */}
        {clickDots.map(dot => (
          <ClickDot
            key={dot.id}
            x={dot.x}
            y={dot.y}
            symbol={dot.symbol}
            age={(Date.now() - dot.timestamp) / 1000}
            onClick={(e) => handleDotClick(dot, e)}
          />
        ))}

        {/* Connections between dots */}
        {connections.map(connection => (
          <ClickConnection
            key={connection.id}
            x1={connection.x1}
            y1={connection.y1}
            x2={connection.x2}
            y2={connection.y2}
            opacity={connection.opacity}
          />
        ))}
      </InteractiveContainer>

      <ContentCard>
        <LogoContainer>
          <Logo />
          <BrandName>Findy.AI</BrandName>
        </LogoContainer>
        <Title>AI Software Engineer</Title>
        <Subtitle>
          End-to-end software development
        </Subtitle>
        <StartButton onClick={handleStart}>
          {isAuthenticated ? 'Start Task' : 'Sign In to Start'}
        </StartButton>
      </ContentCard>

      {showLoginModal && (
        <LoginModal
          isOpen={showLoginModal}
          onClose={() => setShowLoginModal(false)}
        />
      )}
    </WelcomeContainer>
  );
};

export default WelcomeScreen;

import React, { useState } from 'react';
import styled from 'styled-components';
import { Task } from '../App';

const ResourceContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background: rgba(255, 255, 255, 0.02);
  border-left: 1px solid rgba(255, 255, 255, 0.08);
`;

const ResourceHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.03);
`;

const HeaderTitle = styled.h2`
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
`;

const ThemeToggle = styled.button`
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
  }
`;

const ResourceContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }
`;

const Section = styled.div`
  margin-bottom: 20px;
`;

const SectionHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  cursor: pointer;
  padding: 6px 0;
`;

const SectionTitle = styled.h3`
  font-size: 13px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 6px;
`;

const CollapseIcon = styled.span<{ isExpanded: boolean }>`
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
  transform: ${props => props.isExpanded ? 'rotate(90deg)' : 'rotate(0deg)'};
  transition: transform 0.2s ease;
`;

const SectionContent = styled.div<{ isExpanded: boolean }>`
  max-height: ${props => props.isExpanded ? '1000px' : '0'};
  overflow: hidden;
  transition: max-height 0.3s ease;
`;

const ResourceItem = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 6px;
  margin-bottom: 6px;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
  }
`;

const ResourceInfo = styled.div`
  flex: 1;
`;

const ResourceName = styled.div`
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2px;
`;

const ResourceDetail = styled.div`
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
`;

const ResourceStatus = styled.div<{ status: string }>`
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 9px;
  font-weight: 500;
  text-transform: uppercase;
  background: ${props => {
    switch (props.status) {
      case 'active': return 'rgba(0, 255, 136, 0.2)';
      case 'inactive': return 'rgba(255, 255, 255, 0.1)';
      case 'error': return 'rgba(255, 68, 68, 0.2)';
      case 'warning': return 'rgba(255, 170, 0, 0.2)';
      default: return 'rgba(255, 255, 255, 0.1)';
    }
  }};
  color: ${props => {
    switch (props.status) {
      case 'active': return '#00ff88';
      case 'inactive': return 'rgba(255, 255, 255, 0.6)';
      case 'error': return '#ff4444';
      case 'warning': return '#ffaa00';
      default: return 'rgba(255, 255, 255, 0.6)';
    }
  }};
`;

const ProgressBar = styled.div<{ value: number; max: number }>`
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 6px;
  
  &::after {
    content: '';
    display: block;
    width: ${props => (props.value / props.max) * 100}%;
    height: 100%;
    background: ${props => {
    const percentage = (props.value / props.max) * 100;
    if (percentage > 80) return '#ff4444';
    if (percentage > 60) return '#ffaa00';
    return '#00ff88';
  }};
    transition: width 0.3s ease;
  }
`;

const UsageText = styled.div`
  font-size: 9px;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 3px;
  text-align: right;
`;

const ActionButton = styled.button`
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  font-size: 10px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
  }
`;

interface ResourcePanelProps {
  activeTask: Task | null;
  onToggleTheme: () => void;
  isDarkTheme: boolean;
}

const ResourcePanel: React.FC<ResourcePanelProps> = ({
  activeTask,
  onToggleTheme,
  isDarkTheme
}) => {
  const [expandedSections, setExpandedSections] = useState({
    stack: true,
    tools: true,
    resources: true,
    files: false
  });

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Mock data
  const stackData = [
    { name: 'React', version: '18.2.0', status: 'active' },
    { name: 'TypeScript', version: '4.9.5', status: 'active' },
    { name: 'Node.js', version: '18.17.0', status: 'active' },
    { name: 'PostgreSQL', version: '15.3', status: 'active' }
  ];

  const toolsData = [
    { name: 'VS Code', version: '1.85.0', type: 'IDE' },
    { name: 'Git', version: '2.41.0', type: 'VCS' },
    { name: 'Docker', version: '24.0.2', type: 'Container' },
    { name: 'ESLint', version: '8.45.0', type: 'Linter' }
  ];

  const resourcesData = [
    { name: 'CPU', value: 25, max: 100, unit: '%' },
    { name: 'Memory', value: 1.8, max: 8, unit: 'GB' },
    { name: 'Storage', value: 45, max: 100, unit: 'GB' },
    { name: 'Network', value: 2.1, max: 100, unit: 'Mbps' }
  ];

  return (
    <ResourceContainer>
      <ResourceHeader>
        <HeaderTitle>Resources</HeaderTitle>
        <ThemeToggle onClick={onToggleTheme}>
          {isDarkTheme ? '☀' : '☾'}
        </ThemeToggle>
      </ResourceHeader>

      <ResourceContent>
        <Section>
          <SectionHeader onClick={() => toggleSection('stack')}>
            <SectionTitle>
              Tech Stack
              <CollapseIcon isExpanded={expandedSections.stack}>▶</CollapseIcon>
            </SectionTitle>
          </SectionHeader>
          <SectionContent isExpanded={expandedSections.stack}>
            {stackData.map((item, index) => (
              <ResourceItem key={index}>
                <ResourceInfo>
                  <ResourceName>{item.name}</ResourceName>
                  <ResourceDetail>v{item.version}</ResourceDetail>
                </ResourceInfo>
                <ResourceStatus status={item.status}>
                  {item.status}
                </ResourceStatus>
              </ResourceItem>
            ))}
          </SectionContent>
        </Section>

        <Section>
          <SectionHeader onClick={() => toggleSection('tools')}>
            <SectionTitle>
              Development Tools
              <CollapseIcon isExpanded={expandedSections.tools}>▶</CollapseIcon>
            </SectionTitle>
          </SectionHeader>
          <SectionContent isExpanded={expandedSections.tools}>
            {toolsData.map((tool, index) => (
              <ResourceItem key={index}>
                <ResourceInfo>
                  <ResourceName>{tool.name}</ResourceName>
                  <ResourceDetail>{tool.type} • v{tool.version}</ResourceDetail>
                </ResourceInfo>
                <ActionButton>Config</ActionButton>
              </ResourceItem>
            ))}
          </SectionContent>
        </Section>

        <Section>
          <SectionHeader onClick={() => toggleSection('resources')}>
            <SectionTitle>
              System Resources
              <CollapseIcon isExpanded={expandedSections.resources}>▶</CollapseIcon>
            </SectionTitle>
          </SectionHeader>
          <SectionContent isExpanded={expandedSections.resources}>
            {resourcesData.map((resource, index) => (
              <ResourceItem key={index}>
                <ResourceInfo>
                  <ResourceName>{resource.name}</ResourceName>
                  <ProgressBar value={resource.value} max={resource.max} />
                  <UsageText>
                    {resource.value} / {resource.max} {resource.unit}
                  </UsageText>
                </ResourceInfo>
              </ResourceItem>
            ))}
          </SectionContent>
        </Section>

        {activeTask && (
          <Section>
            <SectionHeader onClick={() => toggleSection('files')}>
              <SectionTitle>
                Task Files
                <CollapseIcon isExpanded={expandedSections.files}>▶</CollapseIcon>
              </SectionTitle>
            </SectionHeader>
            <SectionContent isExpanded={expandedSections.files}>
              <ResourceItem>
                <ResourceInfo>
                  <ResourceName>src/app.ts</ResourceName>
                  <ResourceDetail>Entry point • 2m ago</ResourceDetail>
                </ResourceInfo>
              </ResourceItem>
              <ResourceItem>
                <ResourceInfo>
                  <ResourceName>package.json</ResourceName>
                  <ResourceDetail>Dependencies • 5m ago</ResourceDetail>
                </ResourceInfo>
              </ResourceItem>
              <ResourceItem>
                <ResourceInfo>
                  <ResourceName>README.md</ResourceName>
                  <ResourceDetail>Documentation • 1m ago</ResourceDetail>
                </ResourceInfo>
              </ResourceItem>
            </SectionContent>
          </Section>
        )}
      </ResourceContent>
    </ResourceContainer>
  );
};

export default ResourcePanel; 
import React, { useState, useRef, useCallback } from "react";
import styled from "styled-components";
// import Terminal from './Terminal'; // Disabled for web version

const PanelContainer = styled.div<{ height: number }>`
	height: ${(props) => props.height}px;
	background-color: ${(props) => props.theme.colors.panelBackground};
	border-top: ${(props) => props.theme.sizes.borderWidth} solid
		${(props) => props.theme.colors.panelBorder};
	display: flex;
	flex-direction: column;
	position: relative;
	min-height: 100px;
	max-height: 400px;
`;

const PanelHeader = styled.div`
	height: 35px;
	display: flex;
	align-items: center;
	background-color: ${(props) => props.theme.colors.panelBackground};
	border-bottom: ${(props) => props.theme.sizes.borderWidth} solid
		${(props) => props.theme.colors.panelBorder};
	user-select: none;
`;

const PanelTabs = styled.div`
	display: flex;
	flex: 1;
`;

const PanelTab = styled.div<{ isActive: boolean }>`
	padding: 8px 16px;
	font-size: 13px;
	color: ${(props) =>
		props.isActive
			? props.theme.colors.panelForeground
			: props.theme.colors.inactive};
	cursor: pointer;
	border-bottom: 2px solid
		${(props) => (props.isActive ? props.theme.colors.primary : "transparent")};

	&:hover {
		color: ${(props) => props.theme.colors.panelForeground};
	}
`;

const PanelActions = styled.div`
	display: flex;
	align-items: center;
	padding: 0 8px;
`;

const ActionButton = styled.button`
	background: none;
	border: none;
	color: ${(props) => props.theme.colors.panelForeground};
	cursor: pointer;
	padding: 4px;
	border-radius: 3px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 24px;
	height: 24px;

	&:hover {
		background-color: ${(props) => props.theme.colors.hover};
	}

	svg {
		width: 16px;
		height: 16px;
		fill: currentColor;
	}
`;

const PanelContent = styled.div`
	flex: 1;
	overflow: hidden;
	background-color: ${(props) => props.theme.colors.panelBackground};
`;

const ResizeHandle = styled.div`
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 4px;
	cursor: row-resize;
	background-color: transparent;

	&:hover {
		background-color: ${(props) => props.theme.colors.primary};
	}
`;

const EmptyPanel = styled.div`
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
	color: ${(props) => props.theme.colors.inactive};
	font-size: 13px;
`;

type PanelTabType = "terminal" | "problems" | "output" | "debug";

interface PanelProps {
	height: number;
	onHeightChange: (height: number) => void;
	onClose: () => void;
}

const Panel: React.FC<PanelProps> = ({ height, onHeightChange, onClose }) => {
	const [activeTab, setActiveTab] = useState<PanelTabType>("terminal");
	const [isResizing, setIsResizing] = useState(false);
	const panelRef = useRef<HTMLDivElement>(null);

	const handleMouseDown = useCallback((e: React.MouseEvent) => {
		setIsResizing(true);
		e.preventDefault();
	}, []);

	const handleMouseMove = useCallback(
		(e: MouseEvent) => {
			if (!isResizing || !panelRef.current) return;

			const rect = panelRef.current.getBoundingClientRect();
			const newHeight = rect.bottom - e.clientY;

			if (newHeight >= 100 && newHeight <= 400) {
				onHeightChange(newHeight);
			}
		},
		[isResizing, onHeightChange]
	);

	const handleMouseUp = useCallback(() => {
		setIsResizing(false);
	}, []);

	React.useEffect(() => {
		if (isResizing) {
			document.addEventListener("mousemove", handleMouseMove);
			document.addEventListener("mouseup", handleMouseUp);

			return () => {
				document.removeEventListener("mousemove", handleMouseMove);
				document.removeEventListener("mouseup", handleMouseUp);
			};
		}
	}, [isResizing, handleMouseMove, handleMouseUp]);

	const tabs: { id: PanelTabType; label: string; count?: number }[] = [
		{ id: "terminal", label: "Terminal" },
		{ id: "problems", label: "Problems", count: 0 },
		{ id: "output", label: "Output" },
		{ id: "debug", label: "Debug Console" },
	];

	const renderTabContent = () => {
		switch (activeTab) {
			case "terminal":
				return <EmptyPanel>Terminal functionality coming soon...</EmptyPanel>;
			case "problems":
				return <EmptyPanel>No problems detected in the workspace.</EmptyPanel>;
			case "output":
				return <EmptyPanel>No output available.</EmptyPanel>;
			case "debug":
				return <EmptyPanel>Debug console is not active.</EmptyPanel>;
			default:
				return null;
		}
	};

	return (
		<PanelContainer ref={panelRef} height={height}>
			<ResizeHandle onMouseDown={handleMouseDown} />
			<PanelHeader>
				<PanelTabs>
					{tabs.map((tab) => (
						<PanelTab
							key={tab.id}
							isActive={activeTab === tab.id}
							onClick={() => setActiveTab(tab.id)}
						>
							{tab.label}
							{tab.count !== undefined && tab.count > 0 && ` (${tab.count})`}
						</PanelTab>
					))}
				</PanelTabs>
				<PanelActions>
					<ActionButton onClick={onClose} title="Close Panel">
						<svg viewBox="0 0 24 24">
							<path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
						</svg>
					</ActionButton>
				</PanelActions>
			</PanelHeader>
			<PanelContent>{renderTabContent()}</PanelContent>
		</PanelContainer>
	);
};

export default Panel;

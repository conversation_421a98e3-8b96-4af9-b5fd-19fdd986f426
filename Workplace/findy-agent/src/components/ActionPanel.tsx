import React, { useEffect, useRef } from 'react';
import styled, { keyframes } from 'styled-components';
import { Task } from '../App';

const pulse = keyframes`
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
`;

const slideIn = keyframes`
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
`;

const ActionContainer = styled.div<{ isVisible: boolean }>`
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background: rgba(255, 255, 255, 0.02);
  animation: ${slideIn} 0.3s ease-out;
  position: relative;
`;

const ActionHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
`;

const HeaderTitle = styled.h2`
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 6px;
`;

const StatusIndicator = styled.div<{ isActive: boolean }>`
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: ${props => props.isActive ? '#00ff88' : 'rgba(255, 255, 255, 0.3)'};
  animation: ${props => props.isActive ? pulse : 'none'} 2s infinite;
`;

const CloseButton = styled.button`
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
  }
`;

const ActionsContainer = styled.div`
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }
`;

const ActionItem = styled.div<{ status: string }>`
  display: flex;
  align-items: flex-start;
  gap: 10px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-left: 2px solid ${props => {
    switch (props.status) {
      case 'processing': return '#ffaa00';
      case 'completed': return '#00ff88';
      case 'error': return '#ff4444';
      default: return 'rgba(255, 255, 255, 0.1)';
    }
  }};
  border-radius: 6px;
  transition: all 0.2s ease;
  animation: ${slideIn} 0.3s ease-out;
  
  &:hover {
    background: rgba(255, 255, 255, 0.05);
  }
`;

const ActionIcon = styled.div<{ status: string }>`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: ${props => {
    switch (props.status) {
      case 'processing': return 'rgba(255, 170, 0, 0.2)';
      case 'completed': return 'rgba(0, 255, 136, 0.2)';
      case 'error': return 'rgba(255, 68, 68, 0.2)';
      default: return 'rgba(255, 255, 255, 0.1)';
    }
  }};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: ${props => {
    switch (props.status) {
      case 'processing': return '#ffaa00';
      case 'completed': return '#00ff88';
      case 'error': return '#ff4444';
      default: return 'rgba(255, 255, 255, 0.6)';
    }
  }};
  flex-shrink: 0;
  animation: ${props => props.status === 'processing' ? pulse : 'none'} 2s infinite;
`;

const ActionContent = styled.div`
  flex: 1;
`;

const ActionTitle = styled.h4`
  margin: 0 0 2px 0;
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
`;

const ActionDescription = styled.p`
  margin: 0 0 6px 0;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.3;
`;

const ActionTimestamp = styled.span`
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 400;
`;

const ProgressBar = styled.div<{ progress: number }>`
  width: 100%;
  height: 2px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 1px;
  overflow: hidden;
  margin-top: 6px;
  
  &::after {
    content: '';
    display: block;
    width: ${props => props.progress}%;
    height: 100%;
    background: linear-gradient(90deg, #00ff88 0%, #0099ff 100%);
    transition: width 0.3s ease;
  }
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: rgba(255, 255, 255, 0.5);
  font-style: italic;
`;

const EmptyIcon = styled.div`
  font-size: 32px;
  margin-bottom: 12px;
  opacity: 0.3;
`;

interface ActionPanelProps {
  isVisible: boolean;
  actions: any[];
  activeTask: Task | null;
  onClose: () => void;
}

const ActionPanel: React.FC<ActionPanelProps> = ({
  isVisible,
  actions,
  activeTask,
  onClose
}) => {
  const actionsEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    actionsEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    if (actions.length > 0) {
      scrollToBottom();
    }
  }, [actions]);

  const getActionIcon = (type: string, status: string) => {
    if (status === 'processing') return '⚡';
    if (status === 'completed') return '✓';
    if (status === 'error') return '✗';

    switch (type) {
      case 'analysis': return '🔍';
      case 'design': return '📐';
      case 'code': return '⌨';
      case 'test': return '🧪';
      case 'deploy': return '🚀';
      case 'file': return '📄';
      default: return '⚙';
    }
  };

  const getActionTitle = (type: string) => {
    switch (type) {
      case 'analysis': return 'Requirements Analysis';
      case 'design': return 'System Design';
      case 'code': return 'Code Implementation';
      case 'test': return 'Testing';
      case 'deploy': return 'Deployment';
      case 'file': return 'File Operation';
      default: return 'Development Task';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const isAgentActive = actions.some(action => action.status === 'processing');

  return (
    <ActionContainer isVisible={isVisible}>
      <ActionHeader>
        <HeaderTitle>
          <StatusIndicator isActive={isAgentActive} />
          Development Process
        </HeaderTitle>
        <CloseButton onClick={onClose}>
          ×
        </CloseButton>
      </ActionHeader>

      <ActionsContainer>
        {actions.length === 0 ? (
          <EmptyState>
            <EmptyIcon>⚙</EmptyIcon>
            <div style={{ fontSize: '12px' }}>
              Development steps will appear here
              <br />
              Start discussing to see progress
            </div>
          </EmptyState>
        ) : (
          <>
            {actions.map((action, index) => (
              <ActionItem key={index} status={action.status}>
                <ActionIcon status={action.status}>
                  {getActionIcon(action.type, action.status)}
                </ActionIcon>
                <ActionContent>
                  <ActionTitle>
                    {getActionTitle(action.type)}
                  </ActionTitle>
                  <ActionDescription>
                    {action.content || action.description || 'Processing...'}
                  </ActionDescription>
                  <ActionTimestamp>
                    {formatTimestamp(action.timestamp)}
                  </ActionTimestamp>
                  {action.progress !== undefined && (
                    <ProgressBar progress={action.progress} />
                  )}
                </ActionContent>
              </ActionItem>
            ))}
            <div ref={actionsEndRef} />
          </>
        )}
      </ActionsContainer>
    </ActionContainer>
  );
};

export default ActionPanel; 
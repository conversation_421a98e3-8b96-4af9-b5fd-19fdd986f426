import React from 'react';
import styled from 'styled-components';
import { useEditor } from '../contexts/EditorContext';

const StatusBarContainer = styled.div`
  height: ${props => props.theme.sizes.statusBarHeight};
  background-color: ${props => props.theme.colors.statusBarBackground};
  color: ${props => props.theme.colors.statusBarForeground};
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;
  font-size: 12px;
  user-select: none;
  border-top: ${props => props.theme.sizes.borderWidth} solid ${props => props.theme.colors.statusBarBorder};
`;

const StatusSection = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
`;

const StatusItem = styled.div<{ clickable?: boolean }>`
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  border-radius: 3px;
  cursor: ${props => props.clickable ? 'pointer' : 'default'};
  
  ${props => props.clickable && `
    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  `}
`;

const StatusIcon = styled.span`
  font-size: 10px;
`;

const BranchIcon = styled.span`
  &::before {
    content: '⎇';
    margin-right: 4px;
  }
`;

const ErrorIcon = styled.span`
  color: ${props => props.theme.colors.error};
  &::before {
    content: '✕';
    margin-right: 4px;
  }
`;

const WarningIcon = styled.span`
  color: ${props => props.theme.colors.warning};
  &::before {
    content: '⚠';
    margin-right: 4px;
  }
`;

const InfoIcon = styled.span`
  color: ${props => props.theme.colors.info};
  &::before {
    content: 'ℹ';
    margin-right: 4px;
  }
`;

interface StatusBarProps {
  isDarkTheme: boolean;
  onToggleTheme: () => void;
  onTogglePanel: () => void;
}

const StatusBar: React.FC<StatusBarProps> = ({ isDarkTheme, onToggleTheme, onTogglePanel }) => {
  const { getActiveTab } = useEditor();
  const activeTab = getActiveTab();

  const getFileInfo = () => {
    if (!activeTab) return null;
    
    const lines = activeTab.content.split('\n').length;
    const chars = activeTab.content.length;
    
    return {
      language: activeTab.language || 'plaintext',
      lines,
      chars,
      encoding: 'UTF-8',
      lineEnding: 'LF',
    };
  };

  const fileInfo = getFileInfo();

  return (
    <StatusBarContainer>
      <StatusSection>
        <StatusItem clickable>
          <BranchIcon />
          main
        </StatusItem>
        
        <StatusItem>
          <ErrorIcon />
          0
        </StatusItem>
        
        <StatusItem>
          <WarningIcon />
          0
        </StatusItem>
        
        <StatusItem>
          <InfoIcon />
          0
        </StatusItem>
      </StatusSection>

      <StatusSection>
        {fileInfo && (
          <>
            <StatusItem clickable>
              Ln {1}, Col {1}
            </StatusItem>
            
            <StatusItem clickable>
              {fileInfo.language.toUpperCase()}
            </StatusItem>
            
            <StatusItem clickable>
              {fileInfo.encoding}
            </StatusItem>
            
            <StatusItem clickable>
              {fileInfo.lineEnding}
            </StatusItem>
          </>
        )}
        
        <StatusItem clickable onClick={onToggleTheme}>
          {isDarkTheme ? '🌙' : '☀️'}
        </StatusItem>
        
        <StatusItem clickable onClick={onTogglePanel}>
          <StatusIcon>⚡</StatusIcon>
          Terminal
        </StatusItem>
        
        <StatusItem>
          React VSCode
        </StatusItem>
      </StatusSection>
    </StatusBarContainer>
  );
};

export default StatusBar;

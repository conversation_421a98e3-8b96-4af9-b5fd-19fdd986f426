import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { Task } from '../App';

const ChatContainer = styled.div<{ isMinimized: boolean }>`
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  background: rgba(255, 255, 255, 0.02);
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
`;

const ChatHeader = styled.div<{ isMinimized: boolean }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${props => props.isMinimized ? '12px 8px' : '12px 16px'};
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
`;

const HeaderTitle = styled.h2<{ isMinimized: boolean }>`
  font-size: ${props => props.isMinimized ? '0' : '14px'};
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  transition: all 0.3s ease;
  opacity: ${props => props.isMinimized ? 0 : 1};
`;

const ToggleButton = styled.button`
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
  }
`;

const MessagesContainer = styled.div<{ isMinimized: boolean }>`
  flex: 1;
  padding: ${props => props.isMinimized ? '0' : '16px'};
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  opacity: ${props => props.isMinimized ? 0 : 1};
  transition: all 0.3s ease;
  
  &::-webkit-scrollbar {
    width: 4px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }
`;

const Message = styled.div<{ isUser: boolean }>`
  display: flex;
  align-items: flex-start;
  gap: 8px;
  max-width: 80%;
  align-self: ${props => props.isUser ? 'flex-end' : 'flex-start'};
`;

const Avatar = styled.div<{ isUser: boolean }>`
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: ${props => props.isUser
    ? 'linear-gradient(135deg, #00ff88 0%, #0099ff 100%)'
    : 'rgba(255, 255, 255, 0.1)'
  };
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: ${props => props.isUser ? '#000' : 'rgba(255, 255, 255, 0.8)'};
  flex-shrink: 0;
`;

const MessageBubble = styled.div<{ isUser: boolean }>`
  background: ${props => props.isUser
    ? 'rgba(255, 255, 255, 0.1)'
    : 'rgba(255, 255, 255, 0.05)'
  };
  color: rgba(255, 255, 255, 0.9);
  padding: 8px 12px;
  border-radius: ${props => props.isUser
    ? '12px 12px 4px 12px'
    : '12px 12px 12px 4px'
  };
  font-size: 13px;
  line-height: 1.4;
  word-wrap: break-word;
  border: 1px solid ${props => props.isUser
    ? 'rgba(255, 255, 255, 0.2)'
    : 'rgba(255, 255, 255, 0.08)'
  };
`;

const InputArea = styled.div<{ isMinimized: boolean }>`
  padding: ${props => props.isMinimized ? '0' : '16px'};
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.02);
  opacity: ${props => props.isMinimized ? 0 : 1};
  transition: all 0.3s ease;
`;

const InputContainer = styled.div`
  display: flex;
  gap: 8px;
  align-items: flex-end;
`;

const TextInput = styled.textarea`
  flex: 1;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 13px;
  font-family: inherit;
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.9);
  resize: none;
  min-height: 36px;
  max-height: 100px;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.08);
  }
  
  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
`;

const SendButton = styled.button`
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
  flex-shrink: 0;
  
  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.15);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const TaskInfo = styled.div<{ isMinimized: boolean }>`
  padding: ${props => props.isMinimized ? '0' : '12px 16px'};
  background: rgba(255, 255, 255, 0.03);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  opacity: ${props => props.isMinimized ? 0 : 1};
  transition: all 0.3s ease;
`;

const TaskTitle = styled.h3`
  margin: 0 0 4px 0;
  font-size: 13px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
`;

const TaskDescription = styled.p`
  margin: 0;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.3;
`;

interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
}

interface ChatPanelProps {
  isMinimized: boolean;
  onToggle: () => void;
  activeTask: Task | null;
  onCreateTask: (title: string, description: string) => void;
  onAgentAction: (action: any) => void;
}

const ChatPanel: React.FC<ChatPanelProps> = ({
  isMinimized,
  onToggle,
  activeTask,
  onCreateTask,
  onAgentAction
}) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = () => {
    if (!inputValue.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputValue.trim(),
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');

    // Simulate AI response
    setTimeout(() => {
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: "Analyzing requirements. Starting development process...",
        isUser: false,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiMessage]);

      // Simulate agent action
      onAgentAction({
        type: 'analysis',
        content: userMessage.content,
        status: 'processing'
      });
    }, 1000);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <ChatContainer isMinimized={isMinimized}>
      <ChatHeader isMinimized={isMinimized}>
        <HeaderTitle isMinimized={isMinimized}>
          Discussion
        </HeaderTitle>
        <ToggleButton onClick={onToggle}>
          {isMinimized ? '→' : '←'}
        </ToggleButton>
      </ChatHeader>

      {activeTask && (
        <TaskInfo isMinimized={isMinimized}>
          <TaskTitle>{activeTask.title}</TaskTitle>
          <TaskDescription>{activeTask.description}</TaskDescription>
        </TaskInfo>
      )}

      <MessagesContainer isMinimized={isMinimized}>
        {messages.length === 0 && !isMinimized && (
          <div style={{
            textAlign: 'center',
            color: 'rgba(255, 255, 255, 0.5)',
            fontStyle: 'italic',
            fontSize: '12px',
            marginTop: '32px'
          }}>
            Discuss requirements, ask questions, provide feedback...
          </div>
        )}

        {messages.map((message) => (
          <Message key={message.id} isUser={message.isUser}>
            {!message.isUser && <Avatar isUser={false}>SE</Avatar>}
            <MessageBubble isUser={message.isUser}>
              {message.content}
            </MessageBubble>
            {message.isUser && <Avatar isUser={true}>U</Avatar>}
          </Message>
        ))}
        <div ref={messagesEndRef} />
      </MessagesContainer>

      <InputArea isMinimized={isMinimized}>
        <InputContainer>
          <TextInput
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Discuss requirements..."
            rows={1}
          />
          <SendButton
            onClick={sendMessage}
            disabled={!inputValue.trim()}
          >
            ↗
          </SendButton>
        </InputContainer>
      </InputArea>
    </ChatContainer>
  );
};

export default ChatPanel; 
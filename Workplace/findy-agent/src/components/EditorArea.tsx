import React, { useState, useCallback } from 'react';
import styled from 'styled-components';
import { useEditor } from '../contexts/EditorContext';
import TabBar from './TabBar';
import CodeEditor from './CodeEditor';

const EditorContainer = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: ${props => props.theme.colors.editorBackground};
  overflow: hidden;
`;

const EditorContent = styled.div`
  flex: 1;
  display: flex;
  overflow: hidden;
`;

const WelcomeScreen = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: ${props => props.theme.colors.editorBackground};
  color: ${props => props.theme.colors.editorForeground};
  text-align: center;
  padding: 40px;
`;

const WelcomeTitle = styled.h1`
  font-size: 24px;
  font-weight: 300;
  margin-bottom: 20px;
  color: ${props => props.theme.colors.editorForeground};
`;

const WelcomeSubtitle = styled.p`
  font-size: 14px;
  color: ${props => props.theme.colors.inactive};
  margin-bottom: 30px;
  max-width: 400px;
  line-height: 1.5;
`;

const QuickActions = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 30px;
`;

const ActionButton = styled.button`
  background: none;
  border: 1px solid ${props => props.theme.colors.border};
  color: ${props => props.theme.colors.editorForeground};
  padding: 12px 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: ${props => props.theme.colors.hover};
    border-color: ${props => props.theme.colors.primary};
  }
`;

const RecentFiles = styled.div`
  margin-top: 20px;
`;

const RecentFilesTitle = styled.h3`
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
  color: ${props => props.theme.colors.editorForeground};
`;

const RecentFilesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 400px;
`;

const RecentFileItem = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.primary};
  text-align: left;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: ${props => props.theme.colors.hover};
  }
`;

const EditorArea: React.FC = () => {
  const { tabs, getActiveTab } = useEditor();
  const [recentFiles] = useState<string[]>([
    // Mock recent files - in a real app, this would come from storage
    'src/App.tsx',
    'src/components/Header.tsx',
    'package.json',
    'README.md',
  ]);

  const activeTab = getActiveTab();

  const handleNewFile = useCallback(() => {
    // This will be handled by the menu system
  }, []);

  const handleOpenFile = useCallback(() => {
    // This will be handled by the menu system
  }, []);

  const handleOpenFolder = useCallback(() => {
    // This will be handled by the menu system
  }, []);

  const handleOpenRecentFile = useCallback((filePath: string) => {
    // In a real app, this would open the file
    console.log('Opening recent file:', filePath);
  }, []);

  if (tabs.length === 0) {
    return (
      <EditorContainer>
        <WelcomeScreen>
          <WelcomeTitle>React VSCode</WelcomeTitle>
          <WelcomeSubtitle>
            A Visual Studio Code clone built with React and Electron.
            Start by opening a file or folder to begin coding.
          </WelcomeSubtitle>
          
          <QuickActions>
            <ActionButton onClick={handleNewFile}>
              New File
            </ActionButton>
            <ActionButton onClick={handleOpenFile}>
              Open File
            </ActionButton>
            <ActionButton onClick={handleOpenFolder}>
              Open Folder
            </ActionButton>
          </QuickActions>

          {recentFiles.length > 0 && (
            <RecentFiles>
              <RecentFilesTitle>Recent Files</RecentFilesTitle>
              <RecentFilesList>
                {recentFiles.map((file, index) => (
                  <RecentFileItem
                    key={index}
                    onClick={() => handleOpenRecentFile(file)}
                  >
                    {file}
                  </RecentFileItem>
                ))}
              </RecentFilesList>
            </RecentFiles>
          )}
        </WelcomeScreen>
      </EditorContainer>
    );
  }

  return (
    <EditorContainer>
      <TabBar />
      <EditorContent>
        {activeTab && (
          <CodeEditor
            key={activeTab.id}
            tab={activeTab}
          />
        )}
      </EditorContent>
    </EditorContainer>
  );
};

export default EditorArea;

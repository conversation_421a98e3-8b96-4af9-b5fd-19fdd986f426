import React, { useState, useEffect, useCallback } from "react";
import styled from "styled-components";
import { useFileSystem, FileSystemItem } from "../contexts/FileSystemContext";
import { useEditor } from "../contexts/EditorContext";

const ExplorerContainer = styled.div`
	flex: 1;
	overflow-y: auto;
	padding: 8px 0;
`;

const FolderSection = styled.div`
	margin-bottom: 16px;
`;

const SectionHeader = styled.div`
	display: flex;
	align-items: center;
	padding: 4px 16px;
	font-size: 11px;
	font-weight: 600;
	text-transform: uppercase;
	letter-spacing: 0.5px;
	color: ${(props) => props.theme.colors.sidebarForeground};
	cursor: pointer;
	user-select: none;

	&:hover {
		background-color: ${(props) => props.theme.colors.listHoverBackground};
	}
`;

const ChevronIcon = styled.span<{ isExpanded: boolean }>`
	margin-right: 4px;
	transition: transform 0.2s ease;
	transform: ${(props) =>
		props.isExpanded ? "rotate(90deg)" : "rotate(0deg)"};

	&::before {
		content: "▶";
		font-size: 8px;
	}
`;

const FileTree = styled.div`
	padding-left: 8px;
`;

const FileItem = styled.div<{ depth: number; isSelected?: boolean }>`
	display: flex;
	align-items: center;
	padding: 2px 16px 2px ${(props) => 16 + props.depth * 16}px;
	font-size: 13px;
	color: ${(props) => props.theme.colors.sidebarForeground};
	cursor: pointer;
	user-select: none;
	background-color: ${(props) =>
		props.isSelected
			? props.theme.colors.listActiveSelectionBackground
			: "transparent"};

	&:hover {
		background-color: ${(props) =>
			props.isSelected
				? props.theme.colors.listActiveSelectionBackground
				: props.theme.colors.listHoverBackground};
	}
`;

const FileIcon = styled.span<{ isDirectory: boolean; isExpanded?: boolean }>`
	margin-right: 6px;
	font-size: 12px;

	&::before {
		content: ${(props) => {
			if (props.isDirectory) {
				return props.isExpanded ? "'📂'" : "'📁'";
			}
			return "'📄'";
		}};
	}
`;

const EmptyState = styled.div`
	padding: 16px;
	text-align: center;
	color: ${(props) => props.theme.colors.inactive};
	font-size: 13px;
`;

const ActionButton = styled.button`
	background: none;
	border: none;
	color: ${(props) => props.theme.colors.sidebarForeground};
	cursor: pointer;
	padding: 4px 8px;
	margin: 0 4px;
	border-radius: 3px;
	font-size: 11px;

	&:hover {
		background-color: ${(props) => props.theme.colors.listHoverBackground};
	}
`;

const FileExplorer: React.FC = () => {
	const {
		currentWorkspace,
		workspaceHandle,
		openDirectory,
		readFile,
		readDirectory,
		supportsFileSystemAccess,
	} = useFileSystem();
	const { openFile } = useEditor();
	const [fileTree, setFileTree] = useState<FileSystemItem[]>([]);
	const [expandedFolders, setExpandedFolders] = useState<Set<string>>(
		new Set()
	);
	const [selectedFile, setSelectedFile] = useState<string | null>(null);
	const [isWorkspaceExpanded, setIsWorkspaceExpanded] = useState(true);
	const [loading, setLoading] = useState(false);

	const loadDirectoryContents = useCallback(
		async (handle: FileSystemDirectoryHandle): Promise<FileSystemItem[]> => {
			try {
				return await readDirectory(handle);
			} catch (error) {
				console.error("Error reading directory:", error);
				return [];
			}
		},
		[readDirectory]
	);

	const loadWorkspace = useCallback(async () => {
		if (!workspaceHandle) return;

		setLoading(true);
		try {
			const items = await loadDirectoryContents(workspaceHandle);
			setFileTree(items);
		} catch (error) {
			console.error("Error loading workspace:", error);
		} finally {
			setLoading(false);
		}
	}, [workspaceHandle, loadDirectoryContents]);

	useEffect(() => {
		if (workspaceHandle) {
			loadWorkspace();
		}
	}, [workspaceHandle, loadWorkspace]);

	// Simplified for web version - no Electron APIs
	// In a real implementation, you would handle file operations here

	const toggleFolder = useCallback(
		async (item: FileSystemItem) => {
			if (!item.isDirectory || !item.handle) return;

			const isExpanded = expandedFolders.has(item.path);
			const newExpandedFolders = new Set(expandedFolders);

			if (isExpanded) {
				newExpandedFolders.delete(item.path);
			} else {
				newExpandedFolders.add(item.path);

				// Load children if not already loaded
				if (!item.children || item.children.length === 0) {
					try {
						const children = await loadDirectoryContents(
							item.handle as FileSystemDirectoryHandle
						);

						// Update the file tree with the new children
						const updateTree = (items: FileSystemItem[]): FileSystemItem[] => {
							return items.map((treeItem) => {
								if (treeItem.path === item.path) {
									return { ...treeItem, children };
								}
								if (treeItem.children) {
									return {
										...treeItem,
										children: updateTree(treeItem.children),
									};
								}
								return treeItem;
							});
						};

						setFileTree(updateTree(fileTree));
					} catch (error) {
						console.error("Error loading folder contents:", error);
					}
				}
			}

			setExpandedFolders(newExpandedFolders);
		},
		[expandedFolders, fileTree, loadDirectoryContents]
	);

	const handleFileClick = useCallback(
		async (item: FileSystemItem) => {
			if (item.isDirectory) {
				toggleFolder(item);
			} else {
				setSelectedFile(item.path);

				if (item.handle && item.handle.kind === "file") {
					try {
						const fileHandle = item.handle as FileSystemFileHandle;
						const content = await readFile(fileHandle);
						openFile(item.path, content, item.name, fileHandle);
					} catch (error) {
						console.error("Error reading file:", error);
						// Fallback to empty content
						openFile(item.path, "", item.name);
					}
				}
			}
		},
		[toggleFolder, openFile, readFile]
	);

	const renderFileTree = useCallback(
		(items: FileSystemItem[], depth = 0): React.ReactNode => {
			return items.map((item) => (
				<React.Fragment key={item.path}>
					<FileItem
						depth={depth}
						isSelected={selectedFile === item.path}
						onClick={() => handleFileClick(item)}
					>
						{item.isDirectory && (
							<ChevronIcon isExpanded={expandedFolders.has(item.path)} />
						)}
						<FileIcon
							isDirectory={item.isDirectory}
							isExpanded={expandedFolders.has(item.path)}
						/>
						{item.name}
					</FileItem>
					{item.isDirectory &&
						expandedFolders.has(item.path) &&
						item.children &&
						renderFileTree(item.children, depth + 1)}
				</React.Fragment>
			));
		},
		[expandedFolders, selectedFile, handleFileClick]
	);

	const handleOpenFolder = useCallback(async () => {
		await openDirectory();
	}, [openDirectory]);

	if (!currentWorkspace) {
		return (
			<ExplorerContainer>
				<EmptyState>
					<div>No folder opened</div>
					{supportsFileSystemAccess ? (
						<ActionButton onClick={handleOpenFolder}>Open Folder</ActionButton>
					) : (
						<div
							style={{
								fontSize: "12px",
								color: "#888",
								textAlign: "center",
								marginTop: "10px",
							}}
						>
							File System Access API not supported.
							<br />
							Please use Chrome/Edge 86+ or enable the feature flag.
						</div>
					)}
				</EmptyState>
			</ExplorerContainer>
		);
	}

	return (
		<ExplorerContainer>
			<FolderSection>
				<SectionHeader
					onClick={() => setIsWorkspaceExpanded(!isWorkspaceExpanded)}
				>
					<ChevronIcon isExpanded={isWorkspaceExpanded} />
					{currentWorkspace.split("/").pop() || currentWorkspace}
				</SectionHeader>
				{isWorkspaceExpanded && (
					<FileTree>
						{loading ? (
							<div style={{ padding: "16px", color: "#888", fontSize: "13px" }}>
								Loading files...
							</div>
						) : (
							renderFileTree(fileTree)
						)}
					</FileTree>
				)}
			</FolderSection>
		</ExplorerContainer>
	);
};

export default FileExplorer;

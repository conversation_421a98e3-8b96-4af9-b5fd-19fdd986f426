import React, { useState } from 'react';
import styled from 'styled-components';
import { useAuth } from '../contexts/AuthContext';
import { LoginModal } from './auth/LoginModal';

const ProfileContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const Avatar = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #0f0f23;
  font-weight: 600;
  font-size: 14px;
`;

const UserInfo = styled.div`
  display: flex;
  flex-direction: column;
  gap: 2px;
`;

const UserName = styled.div`
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
`;

const UserEmail = styled.div`
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
`;

const ActionButton = styled.button`
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.7);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.9);
  }
`;

const SignInButton = styled.button`
  background: linear-gradient(135deg, #00ff88 0%, #0099ff 100%);
  color: #0f0f23;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 255, 136, 0.3);
  }
`;

export const UserProfile: React.FC = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const [showLoginModal, setShowLoginModal] = useState(false);

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  if (!isAuthenticated) {
    return (
      <>
        <SignInButton onClick={() => setShowLoginModal(true)}>
          Sign In
        </SignInButton>
        <LoginModal 
          isOpen={showLoginModal} 
          onClose={() => setShowLoginModal(false)} 
        />
      </>
    );
  }

  const getInitials = (givenName: string, familyName: string) => {
    return `${givenName.charAt(0)}${familyName.charAt(0)}`.toUpperCase();
  };

  return (
    <ProfileContainer>
      <Avatar>
        {user && getInitials(user.givenName, user.familyName)}
      </Avatar>
      <UserInfo>
        <UserName>
          {user && `${user.givenName} ${user.familyName}`}
        </UserName>
        <UserEmail>{user?.email}</UserEmail>
      </UserInfo>
      <ActionButton onClick={handleLogout}>
        Sign Out
      </ActionButton>
    </ProfileContainer>
  );
};

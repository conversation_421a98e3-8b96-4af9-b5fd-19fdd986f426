import React, { useEffect, useRef, useState, useCallback } from "react";
import styled from "styled-components";
// import { Terminal as XTerm } from "xterm";
// import { FitAddon } from "xterm-addon-fit";
import { useTerminal } from "../contexts/TerminalContext";

// Mock terminal for web version

const TerminalContainer = styled.div`
	display: flex;
	flex-direction: column;
	height: 100%;
	background-color: ${(props) => props.theme.colors.terminalBackground};
`;

const TerminalHeader = styled.div`
	display: flex;
	align-items: center;
	height: 30px;
	background-color: ${(props) => props.theme.colors.panelBackground};
	border-bottom: ${(props) => props.theme.sizes.borderWidth} solid
		${(props) => props.theme.colors.panelBorder};
	padding: 0 8px;
`;

const TerminalTabs = styled.div`
	display: flex;
	flex: 1;
	gap: 4px;
`;

const TerminalTab = styled.div<{ isActive: boolean }>`
	display: flex;
	align-items: center;
	padding: 4px 8px;
	font-size: 12px;
	color: ${(props) =>
		props.isActive
			? props.theme.colors.panelForeground
			: props.theme.colors.inactive};
	background-color: ${(props) =>
		props.isActive ? props.theme.colors.terminalBackground : "transparent"};
	border: ${(props) => props.theme.sizes.borderWidth} solid
		${(props) =>
			props.isActive ? props.theme.colors.panelBorder : "transparent"};
	border-bottom: none;
	border-radius: 4px 4px 0 0;
	cursor: pointer;
	user-select: none;

	&:hover {
		color: ${(props) => props.theme.colors.panelForeground};
	}
`;

const TabCloseButton = styled.button`
	background: none;
	border: none;
	color: inherit;
	cursor: pointer;
	margin-left: 6px;
	padding: 0;
	width: 12px;
	height: 12px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 2px;
	font-size: 10px;

	&:hover {
		background-color: ${(props) => props.theme.colors.hover};
	}

	&::before {
		content: "×";
	}
`;

const TerminalActions = styled.div`
	display: flex;
	align-items: center;
	gap: 4px;
`;

const ActionButton = styled.button`
	background: none;
	border: none;
	color: ${(props) => props.theme.colors.panelForeground};
	cursor: pointer;
	padding: 4px;
	border-radius: 3px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 20px;
	height: 20px;

	&:hover {
		background-color: ${(props) => props.theme.colors.hover};
	}

	svg {
		width: 12px;
		height: 12px;
		fill: currentColor;
	}
`;

const TerminalContent = styled.div`
	flex: 1;
	position: relative;
	overflow: hidden;
`;

const EmptyTerminal = styled.div`
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100%;
	color: ${(props) => props.theme.colors.inactive};
	font-size: 13px;
	gap: 12px;
`;

const CreateTerminalButton = styled.button`
	background-color: ${(props) => props.theme.colors.buttonBackground};
	color: ${(props) => props.theme.colors.buttonForeground};
	border: none;
	padding: 8px 16px;
	border-radius: 4px;
	cursor: pointer;
	font-size: 13px;

	&:hover {
		background-color: ${(props) => props.theme.colors.buttonHoverBackground};
	}
`;

const Terminal: React.FC = () => {
	const {
		terminals,
		activeTerminalId,
		createTerminal,
		closeTerminal,
		setActiveTerminal,
	} = useTerminal();

	const handleCreateTerminal = useCallback(async () => {
		try {
			await createTerminal();
		} catch (error) {
			console.error("Failed to create terminal:", error);
		}
	}, [createTerminal]);

	const handleCloseTerminal = useCallback(
		(terminalId: string) => {
			closeTerminal(terminalId);
		},
		[closeTerminal]
	);

	if (terminals.length === 0) {
		return (
			<TerminalContainer>
				<EmptyTerminal>
					<div>No terminal sessions</div>
					<CreateTerminalButton onClick={handleCreateTerminal}>
						Create New Terminal
					</CreateTerminalButton>
				</EmptyTerminal>
			</TerminalContainer>
		);
	}

	return (
		<TerminalContainer>
			<TerminalHeader>
				<TerminalTabs>
					{terminals.map((terminal) => (
						<TerminalTab
							key={terminal.id}
							isActive={terminal.id === activeTerminalId}
							onClick={() => setActiveTerminal(terminal.id)}
						>
							{terminal.name}
							<TabCloseButton
								onClick={(e) => {
									e.stopPropagation();
									handleCloseTerminal(terminal.id);
								}}
							/>
						</TerminalTab>
					))}
				</TerminalTabs>
				<TerminalActions>
					<ActionButton onClick={handleCreateTerminal} title="New Terminal">
						<svg viewBox="0 0 24 24">
							<path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
						</svg>
					</ActionButton>
				</TerminalActions>
			</TerminalHeader>
			<TerminalContent>
				{terminals.map((terminal) => (
					<div
						key={terminal.id}
						style={{
							display: terminal.id === activeTerminalId ? "flex" : "none",
							alignItems: "center",
							justifyContent: "center",
							height: "100%",
							color: "#888",
							fontSize: "14px",
						}}
					>
						Mock Terminal - {terminal.name}
						<br />
						Terminal functionality will be available in the desktop version.
					</div>
				))}
			</TerminalContent>
		</TerminalContainer>
	);
};

export default Terminal;

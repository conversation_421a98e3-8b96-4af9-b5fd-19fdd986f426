// File System Access API type definitions
interface FileSystemWritableFileStream extends WritableStream {
  write(data: string | BufferSource | Blob): Promise<void>;
  seek(position: number): Promise<void>;
  truncate(size: number): Promise<void>;
  close(): Promise<void>;
}

interface FileSystemFileHandle {
  readonly kind: 'file';
  readonly name: string;
  getFile(): Promise<File>;
  createWritable(options?: { keepExistingData?: boolean }): Promise<FileSystemWritableFileStream>;
  isSameEntry(other: FileSystemHandle): Promise<boolean>;
  queryPermission(options?: { mode?: 'read' | 'readwrite' }): Promise<PermissionState>;
  requestPermission(options?: { mode?: 'read' | 'readwrite' }): Promise<PermissionState>;
}

interface FileSystemDirectoryHandle {
  readonly kind: 'directory';
  readonly name: string;
  entries(): AsyncIterableIterator<[string, FileSystemFileHandle | FileSystemDirectoryHandle]>;
  keys(): AsyncIterableIterator<string>;
  values(): AsyncIterableIterator<FileSystemFileHandle | FileSystemDirectoryHandle>;
  getFileHandle(name: string, options?: { create?: boolean }): Promise<FileSystemFileHandle>;
  getDirectoryHandle(name: string, options?: { create?: boolean }): Promise<FileSystemDirectoryHandle>;
  removeEntry(name: string, options?: { recursive?: boolean }): Promise<void>;
  resolve(possibleDescendant: FileSystemHandle): Promise<string[] | null>;
  isSameEntry(other: FileSystemHandle): Promise<boolean>;
  queryPermission(options?: { mode?: 'read' | 'readwrite' }): Promise<PermissionState>;
  requestPermission(options?: { mode?: 'read' | 'readwrite' }): Promise<PermissionState>;
}

type FileSystemHandle = FileSystemFileHandle | FileSystemDirectoryHandle;

// Extend Window interface for File System Access API
declare global {
  interface Window {
    showDirectoryPicker(options?: {
      id?: string;
      mode?: 'read' | 'readwrite';
      startIn?: FileSystemHandle | string;
    }): Promise<FileSystemDirectoryHandle>;
    showOpenFilePicker(options?: {
      multiple?: boolean;
      excludeAcceptAllOption?: boolean;
      types?: Array<{
        description?: string;
        accept: Record<string, string[]>;
      }>;
      id?: string;
      startIn?: FileSystemHandle | string;
    }): Promise<FileSystemFileHandle[]>;
    showSaveFilePicker(options?: {
      excludeAcceptAllOption?: boolean;
      id?: string;
      startIn?: FileSystemHandle | string;
      suggestedName?: string;
      types?: Array<{
        description?: string;
        accept: Record<string, string[]>;
      }>;
    }): Promise<FileSystemFileHandle>;
  }
}
import 'styled-components';
import { Theme } from './themes';

declare module 'styled-components' {
  export interface DefaultTheme extends Theme { }
}

// Electron API types for web compatibility
declare global {
  interface Window {
    electronAPI?: {
      terminalCreate: (terminalId: string) => Promise<string>;
      terminalWrite: (terminalId: string, data: string) => Promise<void>;
      terminalResize: (terminalId: string, cols: number, rows: number) => Promise<void>;
      terminalKill: (terminalId: string) => Promise<void>;

      readFile: (filePath: string) => Promise<{ success: boolean; content?: string; error?: string }>;
      writeFile: (filePath: string, content: string) => Promise<{ success: boolean; error?: string }>;
      readDirectory: (dirPath: string) => Promise<{ success: boolean; items?: any[]; error?: string }>;

      showSaveDialog: (defaultPath?: string) => Promise<{ canceled: boolean; filePath?: string }>;

      onMenuNewFile: (callback: () => void) => void;
      onMenuOpenFile: (callback: (filePath: string) => void) => void;
      onMenuOpenFolder: (callback: (folderPath: string) => void) => void;
      onMenuSave: (callback: () => void) => void;
      onMenuSaveAs: (callback: () => void) => void;
      onMenuToggleExplorer: (callback: () => void) => void;
      onMenuToggleTerminal: (callback: () => void) => void;

      onTerminalData: (callback: (terminalId: string, data: string) => void) => void;
      onTerminalExit: (callback: (terminalId: string) => void) => void;

      removeAllListeners: (channel: string) => void;
    };
  }
}

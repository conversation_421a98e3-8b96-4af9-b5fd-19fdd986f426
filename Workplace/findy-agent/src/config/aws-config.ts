// AWS Configuration
// These values are from the deployed CDK infrastructure
export const awsConfig = {
  region: process.env.REACT_APP_AWS_REGION || "us-east-1",
  userPoolId: process.env.REACT_APP_USER_POOL_ID || "us-east-1_t0oW1ST1t",
  userPoolClientId:
    process.env.REACT_APP_USER_POOL_CLIENT_ID || "5cejokf0ur5mh78h8lms43i7ae",
  identityPoolId:
    process.env.REACT_APP_IDENTITY_POOL_ID ||
    "us-east-1:170349f4-aed8-4009-b961-41b2c816d4ac",
  apiGatewayUrl:
    process.env.REACT_APP_API_GATEWAY_URL || "http://localhost:3001",
};

// Amplify configuration (when AWS Amplify is installed)
export const amplifyConfig = {
  Auth: {
    region: awsConfig.region,
    userPoolId: awsConfig.userPoolId,
    userPoolWebClientId: awsConfig.userPoolClientId,
    identityPoolId: awsConfig.identityPoolId,
    mandatorySignIn: true,
    authenticationFlowType: "USER_SRP_AUTH",
  },
  API: {
    endpoints: [
      {
        name: "findy-api",
        endpoint: awsConfig.apiGatewayUrl,
        region: awsConfig.region,
      },
    ],
  },
};

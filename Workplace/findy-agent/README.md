# React VSCode

A Visual Studio Code clone built with React.js and Electron, providing a modern code editing experience with familiar VS Code features.

## Features

### ✅ Implemented
- **Modern UI**: VS Code-like interface with activity bar, sidebar, editor area, panel, and status bar
- **File Explorer**: Browse and open files/folders with tree view
- **Code Editor**: Monaco Editor integration with syntax highlighting for multiple languages
- **Tabbed Editing**: Multiple file tabs with dirty state indicators
- **Integrated Terminal**: Full terminal support with xterm.js and node-pty
- **Theme Support**: Dark and light themes with easy switching
- **Keyboard Shortcuts**: Common VS Code shortcuts (Ctrl/Cmd+S, Ctrl/Cmd+O, etc.)
- **File Operations**: Open, save, save as functionality
- **Responsive Layout**: Resizable panels and sidebar

### 🚧 Planned Features
- Search functionality
- Git integration
- Extensions system
- Settings/preferences
- Find and replace
- Command palette
- Debugging support
- Multiple workspaces

## Technology Stack

- **Frontend**: React 18 with TypeScript
- **Desktop**: Electron
- **Code Editor**: Monaco Editor (same as VS Code)
- **Terminal**: xterm.js with node-pty
- **Styling**: Styled Components
- **Build**: React Scripts + Electron Builder

## Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd react-vscode
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm start
```

This will start both the React development server and Electron app.

### Building for Production

1. Build the React app:
```bash
npm run build
```

2. Build the Electron app:
```bash
npm run build:electron
```

3. Create distributable packages:
```bash
npm run dist
```

## Project Structure

```
react-vscode/
├── electron/                 # Electron main process
│   ├── main.ts              # Main Electron process
│   ├── preload.ts           # Preload script for IPC
│   └── tsconfig.json        # TypeScript config for Electron
├── public/                   # Public assets
│   └── index.html           # HTML template
├── src/                      # React source code
│   ├── components/          # React components
│   │   ├── ActivityBar.tsx  # Left activity bar
│   │   ├── CodeEditor.tsx   # Monaco editor wrapper
│   │   ├── EditorArea.tsx   # Main editor area
│   │   ├── FileExplorer.tsx # File tree component
│   │   ├── Panel.tsx        # Bottom panel
│   │   ├── Sidebar.tsx      # Left sidebar
│   │   ├── StatusBar.tsx    # Bottom status bar
│   │   ├── TabBar.tsx       # Editor tabs
│   │   └── Terminal.tsx     # Terminal component
│   ├── contexts/            # React contexts
│   │   ├── EditorContext.tsx    # Editor state management
│   │   ├── FileSystemContext.tsx # File system operations
│   │   └── TerminalContext.tsx   # Terminal management
│   ├── themes/              # Theme definitions
│   │   └── index.ts         # Dark and light themes
│   ├── App.tsx              # Main app component
│   └── index.tsx            # React entry point
├── package.json             # Dependencies and scripts
├── tsconfig.json           # TypeScript configuration
└── README.md               # This file
```

## Key Components

### ActivityBar
- File explorer, search, git, debug, extensions icons
- Toggles sidebar visibility
- VS Code-like styling and behavior

### Sidebar
- File explorer with folder tree
- Expandable/collapsible folders
- File operations (open, create, delete)
- Resizable width

### EditorArea
- Monaco editor integration
- Multiple file tabs
- Syntax highlighting for 20+ languages
- Find/replace, go to line, etc.

### Terminal
- Full terminal emulation with xterm.js
- Multiple terminal tabs
- Process management with node-pty
- Resizable height

### StatusBar
- File information (language, encoding, line/column)
- Git branch status
- Theme toggle
- Error/warning counts

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl/Cmd + N` | New File |
| `Ctrl/Cmd + O` | Open File |
| `Ctrl/Cmd + Shift + O` | Open Folder |
| `Ctrl/Cmd + S` | Save File |
| `Ctrl/Cmd + Shift + S` | Save As |
| `Ctrl/Cmd + Shift + E` | Toggle File Explorer |
| `Ctrl/Cmd + \`` | Toggle Terminal |
| `Ctrl/Cmd + F` | Find in File |
| `Ctrl/Cmd + Shift + F` | Find and Replace |

## Themes

The application supports both dark and light themes that closely match VS Code's appearance:

- **Dark Theme**: Default VS Code dark theme colors
- **Light Theme**: VS Code light theme colors
- **Theme Toggle**: Click the theme icon in the status bar

## File System Integration

- **Open Files**: Drag and drop or use File menu
- **Open Folders**: Full workspace support
- **File Tree**: Expandable folder structure
- **File Operations**: Create, rename, delete files
- **Auto-save**: Optional auto-save functionality

## Terminal Integration

- **Multiple Terminals**: Create and manage multiple terminal sessions
- **Shell Support**: Works with bash, zsh, PowerShell, cmd
- **Process Management**: Full process lifecycle management
- **Resize Support**: Automatic terminal resizing

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes
4. Add tests if applicable
5. Commit your changes: `git commit -am 'Add feature'`
6. Push to the branch: `git push origin feature-name`
7. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- **VS Code Team**: For the amazing editor that inspired this project
- **Monaco Editor**: For providing the excellent code editor component
- **xterm.js**: For the terminal emulation
- **Electron**: For enabling desktop app development with web technologies

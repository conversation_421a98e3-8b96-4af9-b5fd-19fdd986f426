{"name": "react-vscode", "version": "1.0.0", "description": "A Visual Studio Code clone built with React for the web", "main": "build/index.html", "homepage": "./", "private": true, "license": "MIT", "author": "Findy.ai", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@monaco-editor/react": "^4.6.0", "@types/node": "^18.19.0", "monaco-editor": "^0.45.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "styled-components": "^6.1.6", "typescript": "^4.9.5", "uuid": "^9.0.1"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/styled-components": "^5.1.34", "@types/uuid": "^9.0.7"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.reactvscode.app", "productName": "React VSCode", "directories": {"output": "release"}, "files": ["build/**/*", "dist/electron/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.developer-tools"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}
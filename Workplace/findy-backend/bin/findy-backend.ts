#!/usr/bin/env node
import * as cdk from "aws-cdk-lib";
import { FindyCoreStack } from "../lib/findy-core-stack";
import { FindyEcsStack } from "../lib/findy-ecs-stack";
import { FindyApiStack } from "../lib/findy-api-stack";

const app = new cdk.App();

// Deploy core infrastructure first (DynamoDB, Cognito, VPC)
const coreStack = new FindyCoreStack(app, "FindyCoreStack", {
  env: {
    account: process.env.CDK_DEFAULT_ACCOUNT,
    region: process.env.CDK_DEFAULT_REGION,
  },
});

// Deploy ECS infrastructure (depends on core stack)
const ecsStack = new FindyEcsStack(app, "FindyEcsStack", {
  vpcId: coreStack.vpc.vpcId, // This will be ignored, using cross-stack references instead
  tasksTableName: coreStack.tasksTable.tableName,
  usersTableName: coreStack.usersTable.tableName,
  sessionsTableName: coreStack.sessionsTable.tableName,
  tasksTableArn: coreStack.tasksTable.tableArn,
  usersTableArn: coreStack.usersTable.tableArn,
  sessionsTableArn: coreStack.sessionsTable.tableArn,
  userPoolId: coreStack.userPool.userPoolId,
  userPoolClientId: coreStack.userPoolClient.userPoolClientId,
  userPoolArn: coreStack.userPool.userPoolArn,
  env: {
    account: process.env.CDK_DEFAULT_ACCOUNT,
    region: process.env.CDK_DEFAULT_REGION,
  },
});

// Deploy API Gateway (depends on both core and ECS stacks)
const apiStack = new FindyApiStack(app, "FindyApiStack", {
  userPoolId: coreStack.userPool.userPoolId,
  userPoolClientId: coreStack.userPoolClient.userPoolClientId,
  loadBalancerDnsName: ecsStack.ecsService.loadBalancer.loadBalancerDnsName,
  env: {
    account: process.env.CDK_DEFAULT_ACCOUNT,
    region: process.env.CDK_DEFAULT_REGION,
  },
});

// Set up dependencies
ecsStack.addDependency(coreStack);
apiStack.addDependency(ecsStack);

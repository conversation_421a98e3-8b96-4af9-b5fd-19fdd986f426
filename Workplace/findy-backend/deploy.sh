#!/bin/bash

# Findy Backend Deployment Script

set -e

echo "🚀 Starting Findy Backend Deployment..."

# Check if AWS CLI is configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ AWS CLI is not configured. Please run 'aws configure' first."
    exit 1
fi

# Install dependencies
echo "📦 Installing CDK dependencies..."
npm install

# Build the CDK project
echo "🔨 Building CDK project..."
npm run build

# Bootstrap CDK (if not already done)
echo "🏗️  Bootstrapping CDK..."
npx cdk bootstrap

# Deploy the stacks in order
echo "🚀 Deploying Core stack (DynamoDB, Cognito, VPC)..."
npx cdk deploy FindyCoreStack --require-approval never

echo "🚀 Deploying ECS stack..."
npx cdk deploy FindyEcsStack --require-approval never

echo "🚀 Deploying API Gateway stack..."
npx cdk deploy FindyApiStack --require-approval never

# Get stack outputs from all stacks
echo "📋 Getting stack outputs..."
CORE_OUTPUTS=$(aws cloudformation describe-stacks --stack-name FindyCoreStack --query 'Stacks[0].Outputs' --output json)
ECS_OUTPUTS=$(aws cloudformation describe-stacks --stack-name FindyEcsStack --query 'Stacks[0].Outputs' --output json)
API_OUTPUTS=$(aws cloudformation describe-stacks --stack-name FindyApiStack --query 'Stacks[0].Outputs' --output json)

# Extract values from different stacks
USER_POOL_ID=$(echo $CORE_OUTPUTS | jq -r '.[] | select(.OutputKey=="UserPoolId") | .OutputValue')
USER_POOL_CLIENT_ID=$(echo $CORE_OUTPUTS | jq -r '.[] | select(.OutputKey=="UserPoolClientId") | .OutputValue')
IDENTITY_POOL_ID=$(echo $CORE_OUTPUTS | jq -r '.[] | select(.OutputKey=="IdentityPoolId") | .OutputValue')
API_GATEWAY_URL=$(echo $API_OUTPUTS | jq -r '.[] | select(.OutputKey=="ApiGatewayUrl") | .OutputValue')
LOAD_BALANCER_DNS=$(echo $ECS_OUTPUTS | jq -r '.[] | select(.OutputKey=="LoadBalancerDnsName") | .OutputValue')

# Create environment file for frontend
echo "📝 Creating environment configuration..."
cat > ../findy-agent/.env.local << EOF
REACT_APP_AWS_REGION=us-east-1
REACT_APP_USER_POOL_ID=$USER_POOL_ID
REACT_APP_USER_POOL_CLIENT_ID=$USER_POOL_CLIENT_ID
REACT_APP_IDENTITY_POOL_ID=$IDENTITY_POOL_ID
REACT_APP_API_GATEWAY_URL=$API_GATEWAY_URL
REACT_APP_LOAD_BALANCER_DNS=$LOAD_BALANCER_DNS
EOF

echo "✅ Deployment completed successfully!"
echo ""
echo "📋 Stack Outputs:"
echo "   User Pool ID: $USER_POOL_ID"
echo "   User Pool Client ID: $USER_POOL_CLIENT_ID"
echo "   Identity Pool ID: $IDENTITY_POOL_ID"
echo "   API Gateway URL: $API_GATEWAY_URL"
echo "   Load Balancer DNS: $LOAD_BALANCER_DNS"
echo ""
echo "🎉 Your Findy backend is now deployed!"
echo "   Frontend environment file created at: ../findy-agent/.env.local"
echo ""
echo "Next steps:"
echo "1. Build and deploy your API service Docker image to ECR"
echo "2. Update the ECS service to use your API image"
echo "3. Start your frontend with: cd ../findy-agent && npm start"

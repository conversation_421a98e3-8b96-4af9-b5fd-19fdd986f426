import * as cdk from "aws-cdk-lib";
import { Construct } from "constructs";
import * as dynamodb from "aws-cdk-lib/aws-dynamodb";
import * as cognito from "aws-cdk-lib/aws-cognito";
import * as ecs from "aws-cdk-lib/aws-ecs";
import * as ecsPatterns from "aws-cdk-lib/aws-ecs-patterns";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import * as apigateway from "aws-cdk-lib/aws-apigateway";
import * as iam from "aws-cdk-lib/aws-iam";
import * as logs from "aws-cdk-lib/aws-logs";

export class FindyBackendStack extends cdk.Stack {
  public readonly userPool: cognito.UserPool;
  public readonly userPoolClient: cognito.UserPoolClient;
  public readonly identityPool: cognito.CfnIdentityPool;
  public readonly tasksTable: dynamodb.Table;
  public readonly usersTable: dynamodb.Table;
  public readonly sessionsTable: dynamodb.Table;
  public readonly api: apigateway.RestApi;
  public readonly ecsService: ecsPatterns.ApplicationLoadBalancedFargateService;

  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // Create VPC for ECS
    const vpc = new ec2.Vpc(this, "FindyVpc", {
      maxAzs: 2,
      natGateways: 1,
    });

    // DynamoDB Tables
    this.tasksTable = new dynamodb.Table(this, "TasksTable", {
      tableName: "findy-tasks",
      partitionKey: {
        name: "userId",
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: "taskId",
        type: dynamodb.AttributeType.STRING,
      },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY, // For development
      pointInTimeRecovery: true,
    });

    this.usersTable = new dynamodb.Table(this, "UsersTable", {
      tableName: "findy-users",
      partitionKey: {
        name: "userId",
        type: dynamodb.AttributeType.STRING,
      },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY, // For development
      pointInTimeRecovery: true,
    });

    this.sessionsTable = new dynamodb.Table(this, "SessionsTable", {
      tableName: "findy-sessions",
      partitionKey: {
        name: "sessionId",
        type: dynamodb.AttributeType.STRING,
      },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY, // For development
      timeToLiveAttribute: "ttl",
    });

    // Cognito User Pool
    this.userPool = new cognito.UserPool(this, "FindyUserPool", {
      userPoolName: "findy-user-pool",
      selfSignUpEnabled: true,
      signInAliases: {
        email: true,
        username: true,
      },
      autoVerify: {
        email: true,
      },
      standardAttributes: {
        email: {
          required: true,
          mutable: true,
        },
        givenName: {
          required: true,
          mutable: true,
        },
        familyName: {
          required: true,
          mutable: true,
        },
      },
      passwordPolicy: {
        minLength: 8,
        requireLowercase: true,
        requireUppercase: true,
        requireDigits: true,
        requireSymbols: false,
      },
      accountRecovery: cognito.AccountRecovery.EMAIL_ONLY,
      removalPolicy: cdk.RemovalPolicy.DESTROY, // For development
    });

    // Cognito User Pool Client
    this.userPoolClient = new cognito.UserPoolClient(
      this,
      "FindyUserPoolClient",
      {
        userPool: this.userPool,
        userPoolClientName: "findy-web-client",
        generateSecret: false, // For web applications
        authFlows: {
          userPassword: true,
          userSrp: true,
        },
        oAuth: {
          flows: {
            authorizationCodeGrant: true,
          },
          scopes: [
            cognito.OAuthScope.OPENID,
            cognito.OAuthScope.EMAIL,
            cognito.OAuthScope.PROFILE,
          ],
          callbackUrls: ["http://localhost:3000", "https://your-domain.com"], // Update with your domain
          logoutUrls: ["http://localhost:3000", "https://your-domain.com"],
        },
      }
    );

    // Cognito Identity Pool
    this.identityPool = new cognito.CfnIdentityPool(this, "FindyIdentityPool", {
      identityPoolName: "findy-identity-pool",
      allowUnauthenticatedIdentities: false,
      cognitoIdentityProviders: [
        {
          clientId: this.userPoolClient.userPoolClientId,
          providerName: this.userPool.userPoolProviderName,
        },
      ],
    });

    // ECS Cluster
    const cluster = new ecs.Cluster(this, "FindyCluster", {
      clusterName: "findy-cluster",
      vpc,
      containerInsights: true,
    });

    // ECS Service with Application Load Balancer
    this.ecsService = new ecsPatterns.ApplicationLoadBalancedFargateService(
      this,
      "FindyApiService",
      {
        cluster,
        serviceName: "findy-api-service",
        cpu: 256,
        memoryLimitMiB: 512,
        desiredCount: 1,
        taskImageOptions: {
          image: ecs.ContainerImage.fromRegistry("nginx:latest"), // Placeholder - will be replaced with our API
          containerPort: 3001,
          environment: {
            NODE_ENV: "production",
            TASKS_TABLE_NAME: this.tasksTable.tableName,
            USERS_TABLE_NAME: this.usersTable.tableName,
            SESSIONS_TABLE_NAME: this.sessionsTable.tableName,
            USER_POOL_ID: this.userPool.userPoolId,
            USER_POOL_CLIENT_ID: this.userPoolClient.userPoolClientId,
            AWS_REGION: this.region,
          },
          logDriver: ecs.LogDrivers.awsLogs({
            streamPrefix: "findy-api",
            logGroup: new logs.LogGroup(this, "FindyApiLogGroup", {
              logGroupName: "/ecs/findy-api",
              removalPolicy: cdk.RemovalPolicy.DESTROY,
              retention: logs.RetentionDays.ONE_WEEK,
            }),
          }),
        },
        publicLoadBalancer: true,
        listenerPort: 80,
      }
    );

    // Grant ECS task permissions to access DynamoDB
    this.tasksTable.grantReadWriteData(this.ecsService.taskDefinition.taskRole);
    this.usersTable.grantReadWriteData(this.ecsService.taskDefinition.taskRole);
    this.sessionsTable.grantReadWriteData(
      this.ecsService.taskDefinition.taskRole
    );

    // Grant ECS task permissions to access Cognito
    this.ecsService.taskDefinition.taskRole.attachInlinePolicy(
      new iam.Policy(this, "CognitoAccessPolicy", {
        statements: [
          new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: [
              "cognito-idp:AdminGetUser",
              "cognito-idp:AdminCreateUser",
              "cognito-idp:AdminSetUserPassword",
              "cognito-idp:AdminUpdateUserAttributes",
              "cognito-idp:ListUsers",
            ],
            resources: [this.userPool.userPoolArn],
          }),
        ],
      })
    );

    // API Gateway
    this.api = new apigateway.RestApi(this, "FindyApi", {
      restApiName: "findy-api",
      description: "Findy Backend API",
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: [
          "Content-Type",
          "X-Amz-Date",
          "Authorization",
          "X-Api-Key",
          "X-Amz-Security-Token",
        ],
      },
    });

    // Add Cognito Authorizer
    const authorizer = new apigateway.CognitoUserPoolsAuthorizer(
      this,
      "FindyAuthorizer",
      {
        cognitoUserPools: [this.userPool],
        authorizerName: "findy-authorizer",
      }
    );

    // Create integrations for different endpoints
    const healthIntegration = new apigateway.HttpIntegration(
      `http://${this.ecsService.loadBalancer.loadBalancerDnsName}/health`,
      {
        httpMethod: "GET",
      }
    );

    const tasksGetIntegration = new apigateway.HttpIntegration(
      `http://${this.ecsService.loadBalancer.loadBalancerDnsName}/api/tasks`,
      {
        httpMethod: "GET",
      }
    );

    const tasksPostIntegration = new apigateway.HttpIntegration(
      `http://${this.ecsService.loadBalancer.loadBalancerDnsName}/api/tasks`,
      {
        httpMethod: "POST",
      }
    );

    const taskGetIntegration = new apigateway.HttpIntegration(
      `http://${this.ecsService.loadBalancer.loadBalancerDnsName}/api/tasks/{taskId}`,
      {
        httpMethod: "GET",
        options: {
          requestParameters: {
            "integration.request.path.taskId": "method.request.path.taskId",
          },
        },
      }
    );

    const taskPutIntegration = new apigateway.HttpIntegration(
      `http://${this.ecsService.loadBalancer.loadBalancerDnsName}/api/tasks/{taskId}`,
      {
        httpMethod: "PUT",
        options: {
          requestParameters: {
            "integration.request.path.taskId": "method.request.path.taskId",
          },
        },
      }
    );

    const taskDeleteIntegration = new apigateway.HttpIntegration(
      `http://${this.ecsService.loadBalancer.loadBalancerDnsName}/api/tasks/{taskId}`,
      {
        httpMethod: "DELETE",
        options: {
          requestParameters: {
            "integration.request.path.taskId": "method.request.path.taskId",
          },
        },
      }
    );

    // Health check endpoint (no auth required)
    const healthResource = this.api.root.addResource("health");
    healthResource.addMethod("GET", healthIntegration);

    // API resource
    const apiResource = this.api.root.addResource("api");

    // Protected API routes
    const tasksResource = apiResource.addResource("tasks");
    tasksResource.addMethod("GET", tasksGetIntegration, {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    });
    tasksResource.addMethod("POST", tasksPostIntegration, {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    });

    const taskResource = tasksResource.addResource("{taskId}");
    taskResource.addMethod("GET", taskGetIntegration, {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
      requestParameters: {
        "method.request.path.taskId": true,
      },
    });
    taskResource.addMethod("PUT", taskPutIntegration, {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
      requestParameters: {
        "method.request.path.taskId": true,
      },
    });
    taskResource.addMethod("DELETE", taskDeleteIntegration, {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
      requestParameters: {
        "method.request.path.taskId": true,
      },
    });

    // Outputs
    new cdk.CfnOutput(this, "UserPoolId", {
      value: this.userPool.userPoolId,
      description: "Cognito User Pool ID",
    });

    new cdk.CfnOutput(this, "UserPoolClientId", {
      value: this.userPoolClient.userPoolClientId,
      description: "Cognito User Pool Client ID",
    });

    new cdk.CfnOutput(this, "IdentityPoolId", {
      value: this.identityPool.ref,
      description: "Cognito Identity Pool ID",
    });

    new cdk.CfnOutput(this, "ApiGatewayUrl", {
      value: this.api.url,
      description: "API Gateway URL",
    });

    new cdk.CfnOutput(this, "LoadBalancerDnsName", {
      value: this.ecsService.loadBalancer.loadBalancerDnsName,
      description: "ECS Load Balancer DNS Name",
    });

    new cdk.CfnOutput(this, "TasksTableName", {
      value: this.tasksTable.tableName,
      description: "DynamoDB Tasks Table Name",
    });
  }
}

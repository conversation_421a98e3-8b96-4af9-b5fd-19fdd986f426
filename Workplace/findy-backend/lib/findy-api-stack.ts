import * as cdk from "aws-cdk-lib";
import { Construct } from "constructs";
import * as apigateway from "aws-cdk-lib/aws-apigateway";
import * as cognito from "aws-cdk-lib/aws-cognito";

export interface FindyApiStackProps extends cdk.StackProps {
  userPoolId: string;
  userPoolClientId: string;
  loadBalancerDnsName: string;
}

export class FindyApiStack extends cdk.Stack {
  public readonly api: apigateway.RestApi;

  constructor(scope: Construct, id: string, props: FindyApiStackProps) {
    super(scope, id, props);

    // Import User Pool
    const userPool = cognito.UserPool.fromUserPoolId(
      this,
      "ImportedUserPool",
      props.userPoolId
    );

    // API Gateway
    this.api = new apigateway.RestApi(this, "FindyApi", {
      restApiName: "findy-api",
      description: "Findy Backend API",
      defaultCorsPreflightOptions: {
        allowOrigins: apigateway.Cors.ALL_ORIGINS,
        allowMethods: apigateway.Cors.ALL_METHODS,
        allowHeaders: [
          "Content-Type",
          "X-Amz-Date",
          "Authorization",
          "X-Api-Key",
          "X-Amz-Security-Token",
        ],
      },
    });

    // Add Cognito Authorizer
    const authorizer = new apigateway.CognitoUserPoolsAuthorizer(
      this,
      "FindyAuthorizer",
      {
        cognitoUserPools: [userPool],
        authorizerName: "findy-authorizer",
      }
    );

    // Create integrations for different endpoints
    const healthIntegration = new apigateway.HttpIntegration(
      `http://${props.loadBalancerDnsName}/health`,
      {
        httpMethod: "GET",
      }
    );

    const tasksGetIntegration = new apigateway.HttpIntegration(
      `http://${props.loadBalancerDnsName}/api/tasks`,
      {
        httpMethod: "GET",
      }
    );

    const tasksPostIntegration = new apigateway.HttpIntegration(
      `http://${props.loadBalancerDnsName}/api/tasks`,
      {
        httpMethod: "POST",
      }
    );

    const taskGetIntegration = new apigateway.HttpIntegration(
      `http://${props.loadBalancerDnsName}/api/tasks/{taskId}`,
      {
        httpMethod: "GET",
        options: {
          requestParameters: {
            "integration.request.path.taskId": "method.request.path.taskId",
          },
        },
      }
    );

    const taskPutIntegration = new apigateway.HttpIntegration(
      `http://${props.loadBalancerDnsName}/api/tasks/{taskId}`,
      {
        httpMethod: "PUT",
        options: {
          requestParameters: {
            "integration.request.path.taskId": "method.request.path.taskId",
          },
        },
      }
    );

    const taskDeleteIntegration = new apigateway.HttpIntegration(
      `http://${props.loadBalancerDnsName}/api/tasks/{taskId}`,
      {
        httpMethod: "DELETE",
        options: {
          requestParameters: {
            "integration.request.path.taskId": "method.request.path.taskId",
          },
        },
      }
    );

    // Health check endpoint (no auth required)
    const healthResource = this.api.root.addResource("health");
    healthResource.addMethod("GET", healthIntegration);

    // API resource
    const apiResource = this.api.root.addResource("api");

    // Protected API routes
    const tasksResource = apiResource.addResource("tasks");
    tasksResource.addMethod("GET", tasksGetIntegration, {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    });
    tasksResource.addMethod("POST", tasksPostIntegration, {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
    });

    const taskResource = tasksResource.addResource("{taskId}");
    taskResource.addMethod("GET", taskGetIntegration, {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
      requestParameters: {
        "method.request.path.taskId": true,
      },
    });
    taskResource.addMethod("PUT", taskPutIntegration, {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
      requestParameters: {
        "method.request.path.taskId": true,
      },
    });
    taskResource.addMethod("DELETE", taskDeleteIntegration, {
      authorizer,
      authorizationType: apigateway.AuthorizationType.COGNITO,
      requestParameters: {
        "method.request.path.taskId": true,
      },
    });

    // Outputs
    new cdk.CfnOutput(this, "ApiGatewayUrl", {
      value: this.api.url,
      description: "API Gateway URL",
      exportName: "FindyApiGatewayUrl",
    });
  }
}

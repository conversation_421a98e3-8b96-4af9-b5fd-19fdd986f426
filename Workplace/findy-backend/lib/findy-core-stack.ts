import * as cdk from "aws-cdk-lib";
import { Construct } from "constructs";
import * as dynamodb from "aws-cdk-lib/aws-dynamodb";
import * as cognito from "aws-cdk-lib/aws-cognito";
import * as ec2 from "aws-cdk-lib/aws-ec2";

export class FindyCoreStack extends cdk.Stack {
  public readonly userPool: cognito.UserPool;
  public readonly userPoolClient: cognito.UserPoolClient;
  public readonly identityPool: cognito.CfnIdentityPool;
  public readonly tasksTable: dynamodb.Table;
  public readonly usersTable: dynamodb.Table;
  public readonly sessionsTable: dynamodb.Table;
  public readonly vpc: ec2.Vpc;

  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // Create VPC for ECS
    this.vpc = new ec2.Vpc(this, "FindyVpc", {
      maxAzs: 2,
      natGateways: 1,
      vpcName: "findy-vpc",
    });

    // DynamoDB Tables
    this.tasksTable = new dynamodb.Table(this, "TasksTable", {
      tableName: "findy-tasks",
      partitionKey: {
        name: "userId",
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: {
        name: "taskId",
        type: dynamodb.AttributeType.STRING,
      },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY, // For development
      pointInTimeRecovery: true,
    });

    this.usersTable = new dynamodb.Table(this, "UsersTable", {
      tableName: "findy-users",
      partitionKey: {
        name: "userId",
        type: dynamodb.AttributeType.STRING,
      },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY, // For development
      pointInTimeRecovery: true,
    });

    this.sessionsTable = new dynamodb.Table(this, "SessionsTable", {
      tableName: "findy-sessions",
      partitionKey: {
        name: "sessionId",
        type: dynamodb.AttributeType.STRING,
      },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY, // For development
      timeToLiveAttribute: "ttl",
    });

    // Cognito User Pool
    this.userPool = new cognito.UserPool(this, "FindyUserPool", {
      userPoolName: "findy-user-pool",
      selfSignUpEnabled: true,
      signInAliases: {
        email: true,
        username: true,
      },
      autoVerify: {
        email: true,
      },
      standardAttributes: {
        email: {
          required: true,
          mutable: true,
        },
        givenName: {
          required: true,
          mutable: true,
        },
        familyName: {
          required: true,
          mutable: true,
        },
      },
      passwordPolicy: {
        minLength: 8,
        requireLowercase: true,
        requireUppercase: true,
        requireDigits: true,
        requireSymbols: false,
      },
      accountRecovery: cognito.AccountRecovery.EMAIL_ONLY,
      removalPolicy: cdk.RemovalPolicy.DESTROY, // For development
    });

    // Cognito User Pool Client
    this.userPoolClient = new cognito.UserPoolClient(
      this,
      "FindyUserPoolClient",
      {
        userPool: this.userPool,
        userPoolClientName: "findy-web-client",
        generateSecret: false, // For web applications
        authFlows: {
          userPassword: true,
          userSrp: true,
        },
        oAuth: {
          flows: {
            authorizationCodeGrant: true,
          },
          scopes: [
            cognito.OAuthScope.OPENID,
            cognito.OAuthScope.EMAIL,
            cognito.OAuthScope.PROFILE,
          ],
          callbackUrls: ["http://localhost:3000", "https://your-domain.com"], // Update with your domain
          logoutUrls: ["http://localhost:3000", "https://your-domain.com"],
        },
      }
    );

    // Cognito Identity Pool
    this.identityPool = new cognito.CfnIdentityPool(this, "FindyIdentityPool", {
      identityPoolName: "findy-identity-pool",
      allowUnauthenticatedIdentities: false,
      cognitoIdentityProviders: [
        {
          clientId: this.userPoolClient.userPoolClientId,
          providerName: this.userPool.userPoolProviderName,
        },
      ],
    });

    // Outputs
    new cdk.CfnOutput(this, "VpcId", {
      value: this.vpc.vpcId,
      description: "VPC ID",
      exportName: "FindyVpcId",
    });

    new cdk.CfnOutput(this, "VpcAzs", {
      value: cdk.Fn.join(",", this.vpc.availabilityZones),
      description: "VPC Availability Zones",
      exportName: "FindyVpcAzs",
    });

    new cdk.CfnOutput(this, "PrivateSubnetIds", {
      value: cdk.Fn.join(
        ",",
        this.vpc.privateSubnets.map((subnet) => subnet.subnetId)
      ),
      description: "Private Subnet IDs",
      exportName: "FindyPrivateSubnetIds",
    });

    new cdk.CfnOutput(this, "PublicSubnetIds", {
      value: cdk.Fn.join(
        ",",
        this.vpc.publicSubnets.map((subnet) => subnet.subnetId)
      ),
      description: "Public Subnet IDs",
      exportName: "FindyPublicSubnetIds",
    });

    new cdk.CfnOutput(this, "UserPoolId", {
      value: this.userPool.userPoolId,
      description: "Cognito User Pool ID",
      exportName: "FindyUserPoolId",
    });

    new cdk.CfnOutput(this, "UserPoolClientId", {
      value: this.userPoolClient.userPoolClientId,
      description: "Cognito User Pool Client ID",
      exportName: "FindyUserPoolClientId",
    });

    new cdk.CfnOutput(this, "IdentityPoolId", {
      value: this.identityPool.ref,
      description: "Cognito Identity Pool ID",
      exportName: "FindyIdentityPoolId",
    });

    new cdk.CfnOutput(this, "TasksTableName", {
      value: this.tasksTable.tableName,
      description: "DynamoDB Tasks Table Name",
      exportName: "FindyTasksTableName",
    });

    new cdk.CfnOutput(this, "UsersTableName", {
      value: this.usersTable.tableName,
      description: "DynamoDB Users Table Name",
      exportName: "FindyUsersTableName",
    });

    new cdk.CfnOutput(this, "SessionsTableName", {
      value: this.sessionsTable.tableName,
      description: "DynamoDB Sessions Table Name",
      exportName: "FindySessionsTableName",
    });

    new cdk.CfnOutput(this, "TasksTableArn", {
      value: this.tasksTable.tableArn,
      description: "DynamoDB Tasks Table ARN",
      exportName: "FindyTasksTableArn",
    });

    new cdk.CfnOutput(this, "UsersTableArn", {
      value: this.usersTable.tableArn,
      description: "DynamoDB Users Table ARN",
      exportName: "FindyUsersTableArn",
    });

    new cdk.CfnOutput(this, "SessionsTableArn", {
      value: this.sessionsTable.tableArn,
      description: "DynamoDB Sessions Table ARN",
      exportName: "FindySessionsTableArn",
    });

    new cdk.CfnOutput(this, "UserPoolArn", {
      value: this.userPool.userPoolArn,
      description: "Cognito User Pool ARN",
      exportName: "FindyUserPoolArn",
    });
  }
}

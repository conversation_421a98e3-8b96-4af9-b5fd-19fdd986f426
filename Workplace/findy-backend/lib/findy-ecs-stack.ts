import * as cdk from "aws-cdk-lib";
import { Construct } from "constructs";
import * as ecs from "aws-cdk-lib/aws-ecs";
import * as ecsPatterns from "aws-cdk-lib/aws-ecs-patterns";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import * as iam from "aws-cdk-lib/aws-iam";
import * as logs from "aws-cdk-lib/aws-logs";

export interface FindyEcsStackProps extends cdk.StackProps {
  vpcId: string;
  tasksTableName: string;
  usersTableName: string;
  sessionsTableName: string;
  tasksTableArn: string;
  usersTableArn: string;
  sessionsTableArn: string;
  userPoolId: string;
  userPoolClientId: string;
  userPoolArn: string;
}

export class FindyEcsStack extends cdk.Stack {
  public readonly ecsService: ecsPatterns.ApplicationLoadBalancedFargateService;
  public readonly cluster: ecs.Cluster;

  constructor(scope: Construct, id: string, props: FindyEcsStackProps) {
    super(scope, id, props);

    // Import VPC from core stack using cross-stack reference
    const vpc = ec2.Vpc.fromVpcAttributes(this, "ImportedVpc", {
      vpcId: cdk.Fn.importValue("FindyVpcId"),
      availabilityZones: cdk.Fn.split(",", cdk.Fn.importValue("FindyVpcAzs")),
      privateSubnetIds: cdk.Fn.split(
        ",",
        cdk.Fn.importValue("FindyPrivateSubnetIds")
      ),
      publicSubnetIds: cdk.Fn.split(
        ",",
        cdk.Fn.importValue("FindyPublicSubnetIds")
      ),
    });

    // ECS Cluster
    this.cluster = new ecs.Cluster(this, "FindyCluster", {
      clusterName: "findy-cluster",
      vpc,
      containerInsights: true,
    });

    // ECS Service with Application Load Balancer
    this.ecsService = new ecsPatterns.ApplicationLoadBalancedFargateService(
      this,
      "FindyApiService",
      {
        cluster: this.cluster,
        serviceName: "findy-api-service",
        cpu: 256,
        memoryLimitMiB: 512,
        desiredCount: 1,
        taskImageOptions: {
          image: ecs.ContainerImage.fromRegistry("nginx:latest"), // Placeholder - will be replaced with our API
          containerPort: 80, // nginx default port
          environment: {
            NODE_ENV: "production",
            TASKS_TABLE_NAME: props.tasksTableName,
            USERS_TABLE_NAME: props.usersTableName,
            SESSIONS_TABLE_NAME: props.sessionsTableName,
            USER_POOL_ID: props.userPoolId,
            USER_POOL_CLIENT_ID: props.userPoolClientId,
            AWS_REGION: this.region,
          },
          logDriver: ecs.LogDrivers.awsLogs({
            streamPrefix: "findy-api",
            logGroup: new logs.LogGroup(this, "FindyApiLogGroup", {
              logGroupName: "/ecs/findy-api",
              removalPolicy: cdk.RemovalPolicy.DESTROY,
              retention: logs.RetentionDays.ONE_WEEK,
            }),
          }),
        },
        publicLoadBalancer: true,
        listenerPort: 80,
      }
    );

    // Grant ECS task permissions to access DynamoDB
    this.ecsService.taskDefinition.taskRole.attachInlinePolicy(
      new iam.Policy(this, "DynamoDBAccessPolicy", {
        statements: [
          new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: [
              "dynamodb:GetItem",
              "dynamodb:PutItem",
              "dynamodb:UpdateItem",
              "dynamodb:DeleteItem",
              "dynamodb:Query",
              "dynamodb:Scan",
            ],
            resources: [
              props.tasksTableArn,
              props.usersTableArn,
              props.sessionsTableArn,
            ],
          }),
        ],
      })
    );

    // Grant ECS task permissions to access Cognito
    this.ecsService.taskDefinition.taskRole.attachInlinePolicy(
      new iam.Policy(this, "CognitoAccessPolicy", {
        statements: [
          new iam.PolicyStatement({
            effect: iam.Effect.ALLOW,
            actions: [
              "cognito-idp:AdminGetUser",
              "cognito-idp:AdminCreateUser",
              "cognito-idp:AdminSetUserPassword",
              "cognito-idp:AdminUpdateUserAttributes",
              "cognito-idp:ListUsers",
            ],
            resources: [props.userPoolArn],
          }),
        ],
      })
    );

    // Outputs
    new cdk.CfnOutput(this, "LoadBalancerDnsName", {
      value: this.ecsService.loadBalancer.loadBalancerDnsName,
      description: "ECS Load Balancer DNS Name",
      exportName: "FindyLoadBalancerDnsName",
    });

    new cdk.CfnOutput(this, "ClusterName", {
      value: this.cluster.clusterName,
      description: "ECS Cluster Name",
      exportName: "FindyClusterName",
    });

    new cdk.CfnOutput(this, "ServiceName", {
      value: this.ecsService.service.serviceName,
      description: "ECS Service Name",
      exportName: "FindyServiceName",
    });
  }
}

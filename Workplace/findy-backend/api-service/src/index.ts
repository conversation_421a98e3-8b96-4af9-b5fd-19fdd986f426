import express from "express";
import cors from "cors";
import helmet from "helmet";
import dotenv from "dotenv";
import { healthRouter } from "./routes/health";
import { tasksRouter } from "./routes/tasks";
import { authenticateToken } from "./middleware/auth";

dotenv.config();

const app = express();
const port = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(
  cors({
    origin: process.env.ALLOWED_ORIGINS?.split(",") || [
      "http://localhost:3000",
    ],
    credentials: true,
  })
);
app.use(express.json());

// Health check route (no auth required)
app.use("/health", healthRouter);

// Protected API routes (authentication required)
app.use("/api/tasks", authenticateToken, tasksRouter);

// Error handling middleware
app.use(
  (
    err: any,
    req: express.Request,
    res: express.Response,
    next: express.NextFunction
  ) => {
    console.error("Error:", err);
    res.status(err.status || 500).json({
      error: err.message || "Internal server error",
    });
  }
);

// 404 handler
app.use("*", (req: express.Request, res: express.Response) => {
  res.status(404).json({ error: "Route not found" });
});

app.listen(port, () => {
  console.log(`Findy API service listening on port ${port}`);
  console.log(`Environment: ${process.env.NODE_ENV}`);
});

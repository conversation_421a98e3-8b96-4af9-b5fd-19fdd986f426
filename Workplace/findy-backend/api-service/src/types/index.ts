import { Request } from "express";

export interface Task {
  userId: string;
  taskId: string;
  title: string;
  description: string;
  status: "active" | "completed" | "paused";
  createdAt: string;
  updatedAt: string;
  progress?: number;
}

export interface User {
  userId: string;
  email: string;
  givenName: string;
  familyName: string;
  createdAt: string;
  updatedAt: string;
}

export interface Session {
  sessionId: string;
  userId: string;
  createdAt: string;
  ttl: number;
}

export interface AuthenticatedRequest extends Request {
  user?: {
    sub: string;
    email: string;
    given_name: string;
    family_name: string;
  };
}

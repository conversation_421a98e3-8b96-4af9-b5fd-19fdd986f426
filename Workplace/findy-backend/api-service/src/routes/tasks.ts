import { Router, Response } from "express";
import { DynamoDBService } from "../services/dynamodb";
import { AuthenticatedRequest } from "../types";

export const tasksRouter = Router();
const dbService = new DynamoDBService();

// GET /api/tasks - Get all tasks for authenticated user
tasksRouter.get("/", async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: "User not authenticated" });
    }

    const userId = req.user.sub;
    const tasks = await dbService.getUserTasks(userId);
    res.json({ tasks });
  } catch (error) {
    console.error("Error fetching tasks:", error);
    res.status(500).json({ error: "Failed to fetch tasks" });
  }
});

// POST /api/tasks - Create a new task
tasksRouter.post("/", async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ error: "User not authenticated" });
    }

    const userId = req.user.sub;
    const { title, description } = req.body;

    if (!title || !description) {
      return res
        .status(400)
        .json({ error: "Title and description are required" });
    }

    // Ensure user exists in our database
    let user = await dbService.getUser(userId);
    if (!user) {
      user = await dbService.createUser(
        userId,
        req.user.email,
        req.user.given_name,
        req.user.family_name
      );
    }

    const task = await dbService.createTask(userId, title, description);
    res.status(201).json({ task });
  } catch (error) {
    console.error("Error creating task:", error);
    res.status(500).json({ error: "Failed to create task" });
  }
});

// GET /api/tasks/:taskId - Get a specific task
tasksRouter.get(
  "/:taskId",
  async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ error: "User not authenticated" });
      }

      const userId = req.user.sub;
      const { taskId } = req.params;

      const task = await dbService.getTask(userId, taskId);
      if (!task) {
        return res.status(404).json({ error: "Task not found" });
      }

      res.json({ task });
    } catch (error) {
      console.error("Error fetching task:", error);
      res.status(500).json({ error: "Failed to fetch task" });
    }
  }
);

// PUT /api/tasks/:taskId - Update a task
tasksRouter.put(
  "/:taskId",
  async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ error: "User not authenticated" });
      }

      const userId = req.user.sub;
      const { taskId } = req.params;

      const { title, description, status, progress } = req.body;
      const updates: any = {};

      if (title !== undefined) updates.title = title;
      if (description !== undefined) updates.description = description;
      if (status !== undefined) {
        if (!["active", "completed", "paused"].includes(status)) {
          return res.status(400).json({ error: "Invalid status value" });
        }
        updates.status = status;
      }
      if (progress !== undefined) {
        if (typeof progress !== "number" || progress < 0 || progress > 100) {
          return res
            .status(400)
            .json({ error: "Progress must be a number between 0 and 100" });
        }
        updates.progress = progress;
      }

      if (Object.keys(updates).length === 0) {
        return res.status(400).json({ error: "No valid updates provided" });
      }

      const task = await dbService.updateTask(userId, taskId, updates);
      if (!task) {
        return res.status(404).json({ error: "Task not found" });
      }

      res.json({ task });
    } catch (error) {
      console.error("Error updating task:", error);
      res.status(500).json({ error: "Failed to update task" });
    }
  }
);

// DELETE /api/tasks/:taskId - Delete a task
tasksRouter.delete(
  "/:taskId",
  async (req: AuthenticatedRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ error: "User not authenticated" });
      }

      const userId = req.user.sub;
      const { taskId } = req.params;

      const deleted = await dbService.deleteTask(userId, taskId);
      if (!deleted) {
        return res.status(404).json({ error: "Task not found" });
      }

      res.json({ message: "Task deleted successfully" });
    } catch (error) {
      console.error("Error deleting task:", error);
      res.status(500).json({ error: "Failed to delete task" });
    }
  }
);

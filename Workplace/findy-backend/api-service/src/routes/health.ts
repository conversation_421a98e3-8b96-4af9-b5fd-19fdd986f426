import { Router } from 'express';

export const healthRouter = Router();

healthRouter.get('/', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'findy-api-service',
    version: '1.0.0',
  });
});

healthRouter.get('/ready', (req, res) => {
  // Add any readiness checks here (database connectivity, etc.)
  res.json({
    status: 'ready',
    timestamp: new Date().toISOString(),
  });
});

import AWS from 'aws-sdk';
import { Task, User, Session } from '../types';
import { v4 as uuidv4 } from 'uuid';

// Configure AWS SDK
AWS.config.update({
  region: process.env.AWS_REGION || 'us-east-1',
});

const dynamodb = new AWS.DynamoDB.DocumentClient();

const TASKS_TABLE = process.env.TASKS_TABLE_NAME || 'findy-tasks';
const USERS_TABLE = process.env.USERS_TABLE_NAME || 'findy-users';
const SESSIONS_TABLE = process.env.SESSIONS_TABLE_NAME || 'findy-sessions';

export class DynamoDBService {
  // Task operations
  async createTask(userId: string, title: string, description: string): Promise<Task> {
    const taskId = uuidv4();
    const now = new Date().toISOString();
    
    const task: Task = {
      userId,
      taskId,
      title,
      description,
      status: 'active',
      createdAt: now,
      updatedAt: now,
      progress: 0,
    };

    await dynamodb.put({
      TableName: TASKS_TABLE,
      Item: task,
    }).promise();

    return task;
  }

  async getTask(userId: string, taskId: string): Promise<Task | null> {
    const result = await dynamodb.get({
      TableName: TASKS_TABLE,
      Key: { userId, taskId },
    }).promise();

    return result.Item as Task || null;
  }

  async getUserTasks(userId: string): Promise<Task[]> {
    const result = await dynamodb.query({
      TableName: TASKS_TABLE,
      KeyConditionExpression: 'userId = :userId',
      ExpressionAttributeValues: {
        ':userId': userId,
      },
      ScanIndexForward: false, // Sort by taskId descending (newest first)
    }).promise();

    return result.Items as Task[] || [];
  }

  async updateTask(userId: string, taskId: string, updates: Partial<Task>): Promise<Task | null> {
    const updateExpression: string[] = [];
    const expressionAttributeNames: { [key: string]: string } = {};
    const expressionAttributeValues: { [key: string]: any } = {};

    // Build update expression dynamically
    Object.keys(updates).forEach((key) => {
      if (key !== 'userId' && key !== 'taskId') {
        updateExpression.push(`#${key} = :${key}`);
        expressionAttributeNames[`#${key}`] = key;
        expressionAttributeValues[`:${key}`] = updates[key as keyof Task];
      }
    });

    // Always update the updatedAt timestamp
    updateExpression.push('#updatedAt = :updatedAt');
    expressionAttributeNames['#updatedAt'] = 'updatedAt';
    expressionAttributeValues[':updatedAt'] = new Date().toISOString();

    const result = await dynamodb.update({
      TableName: TASKS_TABLE,
      Key: { userId, taskId },
      UpdateExpression: `SET ${updateExpression.join(', ')}`,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
      ReturnValues: 'ALL_NEW',
    }).promise();

    return result.Attributes as Task || null;
  }

  async deleteTask(userId: string, taskId: string): Promise<boolean> {
    try {
      await dynamodb.delete({
        TableName: TASKS_TABLE,
        Key: { userId, taskId },
        ConditionExpression: 'attribute_exists(userId)',
      }).promise();
      return true;
    } catch (error: any) {
      if (error.code === 'ConditionalCheckFailedException') {
        return false; // Task doesn't exist
      }
      throw error;
    }
  }

  // User operations
  async createUser(userId: string, email: string, givenName: string, familyName: string): Promise<User> {
    const now = new Date().toISOString();
    
    const user: User = {
      userId,
      email,
      givenName,
      familyName,
      createdAt: now,
      updatedAt: now,
    };

    await dynamodb.put({
      TableName: USERS_TABLE,
      Item: user,
      ConditionExpression: 'attribute_not_exists(userId)', // Only create if doesn't exist
    }).promise();

    return user;
  }

  async getUser(userId: string): Promise<User | null> {
    const result = await dynamodb.get({
      TableName: USERS_TABLE,
      Key: { userId },
    }).promise();

    return result.Item as User || null;
  }

  async updateUser(userId: string, updates: Partial<User>): Promise<User | null> {
    const updateExpression: string[] = [];
    const expressionAttributeNames: { [key: string]: string } = {};
    const expressionAttributeValues: { [key: string]: any } = {};

    Object.keys(updates).forEach((key) => {
      if (key !== 'userId') {
        updateExpression.push(`#${key} = :${key}`);
        expressionAttributeNames[`#${key}`] = key;
        expressionAttributeValues[`:${key}`] = updates[key as keyof User];
      }
    });

    updateExpression.push('#updatedAt = :updatedAt');
    expressionAttributeNames['#updatedAt'] = 'updatedAt';
    expressionAttributeValues[':updatedAt'] = new Date().toISOString();

    const result = await dynamodb.update({
      TableName: USERS_TABLE,
      Key: { userId },
      UpdateExpression: `SET ${updateExpression.join(', ')}`,
      ExpressionAttributeNames: expressionAttributeNames,
      ExpressionAttributeValues: expressionAttributeValues,
      ReturnValues: 'ALL_NEW',
    }).promise();

    return result.Attributes as User || null;
  }

  // Session operations
  async createSession(userId: string): Promise<Session> {
    const sessionId = uuidv4();
    const now = new Date();
    const ttl = Math.floor(now.getTime() / 1000) + (24 * 60 * 60); // 24 hours from now
    
    const session: Session = {
      sessionId,
      userId,
      createdAt: now.toISOString(),
      ttl,
    };

    await dynamodb.put({
      TableName: SESSIONS_TABLE,
      Item: session,
    }).promise();

    return session;
  }

  async getSession(sessionId: string): Promise<Session | null> {
    const result = await dynamodb.get({
      TableName: SESSIONS_TABLE,
      Key: { sessionId },
    }).promise();

    return result.Item as Session || null;
  }

  async deleteSession(sessionId: string): Promise<boolean> {
    try {
      await dynamodb.delete({
        TableName: SESSIONS_TABLE,
        Key: { sessionId },
      }).promise();
      return true;
    } catch (error) {
      console.error('Error deleting session:', error);
      return false;
    }
  }
}

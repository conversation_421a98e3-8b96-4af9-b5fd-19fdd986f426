{"name": "findy-api-service", "version": "1.0.0", "description": "Findy Backend API Service", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "aws-sdk": "^2.1490.0", "uuid": "^9.0.1", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.5", "@types/uuid": "^9.0.7", "@types/node": "^20.9.0", "typescript": "^5.2.2", "ts-node": "^10.9.1"}}
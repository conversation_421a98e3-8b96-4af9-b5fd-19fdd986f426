{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAC/B,8DAAqC;AAGrC,MAAM,MAAM,GAAG,IAAA,qBAAU,EAAC;IACxB,OAAO,EAAE,uBAAuB,OAAO,CAAC,GAAG,CAAC,UAAU,kBAAkB,OAAO,CAAC,GAAG,CAAC,YAAY,wBAAwB;CACzH,CAAC,CAAC;AAEH,SAAS,MAAM,CAAC,MAAW,EAAE,QAAa;IACxC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QAC5C,IAAI,GAAG,EAAE,CAAC;YACR,QAAQ,CAAC,GAAG,CAAC,CAAC;YACd,OAAO;QACT,CAAC;QACD,MAAM,UAAU,GAAG,GAAG,EAAE,YAAY,EAAE,CAAC;QACvC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;AACL,CAAC;AAEM,MAAM,cAAc,GAAG,KAAK,EACjC,GAAyB,EACzB,GAAa,EACb,IAAkB,EAClB,EAAE;IACF,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAE7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACrD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,CAAC,CAAC;QAC9D,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAEtC,uBAAuB;QACvB,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE;YACxB,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB;YACzC,MAAM,EAAE,uBAAuB,OAAO,CAAC,GAAG,CAAC,UAAU,kBAAkB,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE;YACjG,UAAU,EAAE,CAAC,OAAO,CAAC;SACtB,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;YAClB,IAAI,GAAG,EAAE,CAAC;gBACR,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;gBACjD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;YAC1D,CAAC;YAED,2BAA2B;YAC3B,GAAG,CAAC,IAAI,GAAG,OAAc,CAAC;YAC1B,IAAI,EAAE,CAAC;QACT,CAAC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QAC/C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,CAAC,CAAC;IAC1D,CAAC;AACH,CAAC,CAAC;AAjCW,QAAA,cAAc,kBAiCzB"}
#!/bin/bash

# Deploy ECS Infrastructure Only

set -e

echo "🚀 Starting Findy ECS Infrastructure Deployment..."

# Check if AWS CLI is configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ AWS CLI is not configured. Please run 'aws configure' first."
    exit 1
fi

# Check if core stack exists
if ! aws cloudformation describe-stacks --stack-name FindyCoreStack > /dev/null 2>&1; then
    echo "❌ Core stack not found. Please deploy core infrastructure first: ./deploy-core.sh"
    exit 1
fi

# Build the CDK project
echo "🔨 Building CDK project..."
npm run build

# Deploy the ECS stack
echo "🚀 Deploying ECS stack..."
npx cdk deploy FindyEcsStack --require-approval never

# Get stack outputs
echo "📋 Getting ECS stack outputs..."
ECS_OUTPUTS=$(aws cloudformation describe-stacks --stack-name FindyEcsStack --query 'Stacks[0].Outputs' --output json)

# Extract values
LOAD_BALANCER_DNS=$(echo $ECS_OUTPUTS | jq -r '.[] | select(.OutputKey=="LoadBalancerDnsName") | .OutputValue')
CLUSTER_NAME=$(echo $ECS_OUTPUTS | jq -r '.[] | select(.OutputKey=="ClusterName") | .OutputValue')
SERVICE_NAME=$(echo $ECS_OUTPUTS | jq -r '.[] | select(.OutputKey=="ServiceName") | .OutputValue')

echo "✅ ECS infrastructure deployment completed successfully!"
echo ""
echo "📋 ECS Stack Outputs:"
echo "   Load Balancer DNS: $LOAD_BALANCER_DNS"
echo "   Cluster Name: $CLUSTER_NAME"
echo "   Service Name: $SERVICE_NAME"
echo ""
echo "🎉 ECS infrastructure is ready!"
echo ""
echo "Next steps:"
echo "1. Deploy API Gateway: ./deploy-api.sh"
echo "2. Build and deploy your API service Docker image"
echo "3. Update ECS service to use your API image"

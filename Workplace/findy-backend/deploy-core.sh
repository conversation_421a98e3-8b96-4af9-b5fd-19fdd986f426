#!/bin/bash

# Deploy Core Infrastructure Only (DynamoDB, Cognito, VPC)

set -e

echo "🚀 Starting Findy Core Infrastructure Deployment..."

# Check if AWS CLI is configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ AWS CLI is not configured. Please run 'aws configure' first."
    exit 1
fi

# Install dependencies
echo "📦 Installing CDK dependencies..."
npm install

# Build the CDK project
echo "🔨 Building CDK project..."
npm run build

# Bootstrap CDK (if not already done)
echo "🏗️  Bootstrapping CDK..."
npx cdk bootstrap

# Deploy the core stack
echo "🚀 Deploying Core stack (DynamoDB, Cognito, VPC)..."
npx cdk deploy FindyCoreStack --require-approval never

# Get stack outputs
echo "📋 Getting core stack outputs..."
CORE_OUTPUTS=$(aws cloudformation describe-stacks --stack-name FindyCoreStack --query 'Stacks[0].Outputs' --output json)

# Extract values
USER_POOL_ID=$(echo $CORE_OUTPUTS | jq -r '.[] | select(.OutputKey=="UserPoolId") | .OutputValue')
USER_POOL_CLIENT_ID=$(echo $CORE_OUTPUTS | jq -r '.[] | select(.OutputKey=="UserPoolClientId") | .OutputValue')
IDENTITY_POOL_ID=$(echo $CORE_OUTPUTS | jq -r '.[] | select(.OutputKey=="IdentityPoolId") | .OutputValue')
VPC_ID=$(echo $CORE_OUTPUTS | jq -r '.[] | select(.OutputKey=="VpcId") | .OutputValue')

echo "✅ Core infrastructure deployment completed successfully!"
echo ""
echo "📋 Core Stack Outputs:"
echo "   User Pool ID: $USER_POOL_ID"
echo "   User Pool Client ID: $USER_POOL_CLIENT_ID"
echo "   Identity Pool ID: $IDENTITY_POOL_ID"
echo "   VPC ID: $VPC_ID"
echo ""
echo "🎉 Core infrastructure is ready!"
echo ""
echo "Next steps:"
echo "1. Deploy ECS stack: ./deploy-ecs.sh"
echo "2. Deploy API Gateway: ./deploy-api.sh"
echo "3. Or deploy all remaining: ./deploy-remaining.sh"
